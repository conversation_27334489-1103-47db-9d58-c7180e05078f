import { Component, ElementRef, Inject, OnInit, ViewChild } from '@angular/core';
import { I18NService } from '@core';
import { ACLService } from '@delon/acl';
import { ALAIN_I18N_TOKEN } from '@delon/theme';
import { ButtonModel, GridModel, QueryFilerModel } from '@model';
import { ApplicationApiService } from '@service';
import { BtnCellRenderComponent, StatusCellRenderComponent } from '@shared';
import {
  AG_GIRD_CELL_STYLE,
  EVENT_TYPE,
  EXCEL_STYLES_DEFAULT,
  FORM_TYPE,
  LIST_STATUS,
  OVERLAY_LOADING_TEMPLATE,
  OVERLAY_NOROW_TEMPLATE,
  PAGE_SIZE_OPTION_DEFAULT,
  QUERY_FILTER_DEFAULT
} from '@util';
import * as moment from 'moment';
import { NzNotificationService } from 'ng-zorro-antd/notification';
import { Subscription } from 'rxjs';

import { ApplicationImportItemComponent } from '../application-import-item/application-import-item.component';
import { ApplicationItemComponent } from '../application-item/application-item.component';

@Component({
  selector: 'app-application',
  templateUrl: './application.component.html',
  styleUrls: ['./application.component.less']
})
export class ApplicationComponent implements OnInit {
  constructor(
    private applicationApiService: ApplicationApiService,
    private aclService: ACLService,
    private notification: NzNotificationService,
    @Inject(ALAIN_I18N_TOKEN) private i18n: I18NService,
    private elementRef: ElementRef
  ) {
    //#region ag-grid
    this.columnDefs = [
      {
        field: 'index',
        headerName: this.i18n.fanyi('app.common.table.grid-index'),
        width: 110,
        headerCheckboxSelection: true,
        headerCheckboxSelectionFilteredOnly: true,
        checkboxSelection: true
      },
      { field: 'code', headerName: 'Mã', minWidth: 180, flex: 1 },
      { field: 'name', headerName: 'Tên', sortable: true, filter: true, minWidth: 180, flex: 1 },
      { field: 'statusName', headerName: 'Trạng thái', minWidth: 150, cellRenderer: 'statusCellRender' },
      {
        field: 'order',
        headerName: 'Thứ tự sắp xếp',
        sortable: true
      },
      {
        headerName: this.i18n.fanyi('app.common.table.grid-action'),
        minWidth: 150,
        cellRenderer: 'btnCellRender',
        cellRendererParams: {
          infoClicked: (item: any) => this.onViewItem(item),
          editClicked: (item: any) => this.onEditItem(item),
          deleteClicked: (item: any) => this.onDeleteItem(item)
        }
      }
    ];
    this.defaultColDef = {
      // flex: 1,
      minWidth: 100,
      cellStyle: AG_GIRD_CELL_STYLE,
      resizable: true
    };
    this.frameworkComponents = {
      btnCellRender: BtnCellRenderComponent,
      statusCellRender: StatusCellRenderComponent
    };
    this.excelStyles = [...EXCEL_STYLES_DEFAULT];
    //#endregion ag-grid

    //#region Init button
    this.btnAdd = {
      title: this.i18n.fanyi('app.common.button.add'),
      visible: true,
      enable: true,
      grandAccess: true,
      click: ($event: any) => {
        this.onAddItem();
      }
    };
    this.btnExportExcel = {
      title: this.i18n.fanyi('app.common.button.export-excel'),
      visible: true,
      enable: true,
      grandAccess: true,
      click: ($event: any) => {
        this.onExportExcel();
      }
    };
    this.btnImportExcel = {
      title: this.i18n.fanyi('app.common.button.import-excel'),
      visible: true,
      enable: true,
      grandAccess: true,
      click: ($event: any) => {
        this.onImportExcel();
      }
    };
    this.btnDelete = {
      title: this.i18n.fanyi('app.common.button.delete'),
      visible: false,
      enable: true,
      grandAccess: true,
      click: ($event: any) => {
        this.onDeleteItem();
      }
    };
    this.btnSearch = {
      title: this.i18n.fanyi('app.common.button.search'),
      visible: true,
      enable: true,
      grandAccess: true,
      click: ($event: any) => {
        this.initGridData();
      }
    };
    this.btnResetSearch = {
      title: this.i18n.fanyi('app.common.button.reset'),
      visible: true,
      enable: true,
      grandAccess: true,
      click: ($event: any) => {
        this.onResetSearch(false);
      }
    };
    this.btnReload = {
      title: this.i18n.fanyi('app.common.button.refresh'),
      visible: true,
      enable: true,
      grandAccess: true,
      click: ($event: any) => {
        this.onResetSearch(true);
      }
    };
    //#endregion Init button
  }
  @ViewChild(ApplicationItemComponent, { static: false }) itemModal!: { initData: (arg0: {}, arg1: string) => void };
  @ViewChild(ApplicationImportItemComponent, { static: false }) importModal!: { initData: () => void };

  isRenderComplete = false;

  listStatus = LIST_STATUS;
  filter: QueryFilerModel = { ...QUERY_FILTER_DEFAULT };
  pageSizeOptions: any[] = [];

  columnDefs: any[] = [];
  grid: GridModel = {
    dataCount: 0,
    rowData: [],
    totalData: 0
  };
  private gridApi: any;
  private gridColumnApi: any;
  defaultColDef: any;
  rowSelection = 'multiple';
  overlayLoadingTemplate = OVERLAY_LOADING_TEMPLATE;
  overlayNoRowsTemplate = OVERLAY_NOROW_TEMPLATE;
  quickText = '';
  excelStyles: any;
  frameworkComponents: any;

  btnAdd: ButtonModel;
  btnDelete: ButtonModel;
  btnExportExcel: ButtonModel;
  btnImportExcel: ButtonModel;
  btnResetSearch: ButtonModel;
  btnSearch: ButtonModel;
  btnReload: ButtonModel;

  isLoadingDelete = false;
  isShowDelete = false;
  isShowImport = false;

  title = 'Danh sách ứng dụng';
  moduleName = 'Ứng dụng';

  modal: any = {
    type: '',
    item: {},
    isShow: false,
    option: {}
  };

  ngOnInit(): void {
    this.initRightOfUser();
    this.isRenderComplete = true;
  }

  initRightOfUser(): void {
    // this.btnAdd.grandAccess = this.aclService.canAbility('APP-ADD');
    // this.btnDelete.grandAccess = this.aclService.canAbility('APP-DELETE');
    // this.btnExportExcel.grandAccess = this.aclService.canAbility('APP-EXPORT-EXCEL');
  }

  //#region Ag-grid
  onPageSizeChange(): void {
    this.initGridData();
  }

  onPageNumberChange(): void {
    this.initGridData();
  }

  onGridReady(params: any): void {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    this.initGridData();
  }

  onSelectionChanged($event: any): void {
    const selectedRows = this.gridApi.getSelectedRows();
    if (selectedRows.length > 0) {
      this.btnDelete.visible = true;
    } else {
      this.btnDelete.visible = false;
    }
  }

  onCellDoubleClicked($event: any): void {
    this.onViewItem($event.data);
  }
  //#endregion Ag-grid

  //#region Search

  //#endregion Search

  //#region Event
  onExportExcel(): void {
    const params = {
      columnWidth: 100,
      sheetName: this.moduleName,
      exportMode: undefined, // 'xml', // : undefined,
      suppressTextAsCDATA: true,
      rowHeight: undefined,
      fileName: `Danh sách ${this.moduleName} ${moment(new Date()).format('DD-MM-YYYY')}.xlsx`,
      headerRowHeight: undefined, // undefined,
      customHeader: [
        [],
        [
          {
            styleId: 'bigHeader',
            data: {
              type: 'String',
              value: `Danh sách ${this.moduleName} (${moment(new Date()).format('DD-MM-YYYY')})`
            },
            mergeAcross: 3
          }
        ],
        []
      ],
      customFooter: [[]]
    };
    this.gridApi.exportDataAsExcel(params);
  }

  onImportExcel(): void {
    this.isShowImport = true;
    this.importModal.initData();
  }

  onResetSearch(reloadData: boolean): void {
    this.filter.pageNumber = 1;
    this.filter.textSearch = undefined;
    this.filter['status'] = undefined;
    if (reloadData) {
      this.initGridData();
    }
  }

  onAddItem(): void {
    this.modal = {
      type: FORM_TYPE.ADD,
      item: {},
      isShow: true,
      option: {}
    };
    this.itemModal.initData({}, FORM_TYPE.ADD);
  }

  onEditItem(item: any = null): void {
    if (item === null) {
      const selectedRows = this.gridApi.getSelectedRows();
      item = selectedRows[0];
    }
    this.modal = {
      type: FORM_TYPE.EDIT,
      item,
      isShow: true,
      option: {}
    };
    this.itemModal.initData(item, FORM_TYPE.EDIT);
  }

  onViewItem(item: any = null): void {
    if (item === null) {
      const selectedRows = this.gridApi.getSelectedRows();
      item = selectedRows[0];
    }
    this.modal = {
      type: FORM_TYPE.INFO,
      item,
      isShow: true,
      option: {}
    };
    this.itemModal.initData(item, FORM_TYPE.INFO);
  }

  onDeleteItem(item: any = null): void {
    let selectedRows = this.gridApi.getSelectedRows();
    if (item !== null) {
      selectedRows = [];
      selectedRows.push(item);
    }
    const tittle = 'Xác nhận xóa';
    let content = '';
    if (selectedRows.length > 1) {
      content = 'Bạn có chắc chắn muốn xóa các bản ghi này không?';
    } else {
      content = 'Bạn có chắc chắn muốn xóa bản ghi này không?';
    }
    this.isShowDelete = true;
    // this.deleteModal.initData(selectedRows, content);
  }

  //#endregion Event

  //#region Modal

  onModalEventEmmit(event: any): void {
    this.modal.isShow = false;
    if (event.type === EVENT_TYPE.SUCCESS) {
      this.initGridData();
    }
  }

  onDeleteEventEmmit(event: any): void {
    if (event.type === EVENT_TYPE.SUCCESS) {
      this.initGridData();
    } else if (event.type === EVENT_TYPE.CONFIRM) {
      // this.deleteModal.updateIsLoading(true);
      this.deleteListItem(event.listId);
    }
  }

  onImportEventEmmit(event: any): void {
    if (event.type === EVENT_TYPE.SUCCESS) {
      this.initGridData();
    }
  }

  //#endregion Modal

  //#region API Event
  deleteListItem(listId: [string]): Subscription {
    this.isLoadingDelete = true;
    const promise = this.applicationApiService.delete(listId).subscribe({
      next: (res: any) => {
        if (res.data === null || res.data === undefined) {
          this.notification.error(this.i18n.fanyi('app.common.error.title'), `${res.message}`);
          return;
        }
      },
      error: (err: any) => {
        if (err.error) {
          this.notification.error(this.i18n.fanyi('app.common.error.title'), `${err.error.message}`);
        } else {
          this.notification.error(this.i18n.fanyi('app.common.error.title'), `${err.status}`);
        }
      },
      complete: () => (this.isLoadingDelete = false)
    });
    return promise;
  }

  initGridData(): Subscription | undefined {
    this.btnDelete.visible = false;
    this.gridApi.showLoadingOverlay();
    const rs = this.applicationApiService.getFilter(this.filter).subscribe({
      next: (res: any) => {
        if (res.data === null || res.data === undefined) {
          this.notification.error(this.i18n.fanyi('app.common.error.title'), `${res.message}`);
          return;
        }

        const dataResult = res.data;

        let i = (this.filter.pageSize ?? 0) * ((this.filter.pageNumber ?? 0) - 1);

        for (const item of dataResult.data) {
          item.index = ++i;
          item.statusName = this.listStatus.find(x => x.id === item.status)?.name;
          item.infoGrantAccess = true;
          item.editGrantAccess = true;
          item.deleteGrantAccess = true;
        }
        this.grid.totalData = dataResult.totalCount;
        this.grid.dataCount = dataResult.dataCount;
        this.grid.rowData = dataResult.data;
        this.pageSizeOptions = [...PAGE_SIZE_OPTION_DEFAULT];
        // tslint:disable-next-line: variable-name
        this.pageSizeOptions = this.pageSizeOptions.filter(number => {
          return number < dataResult.totalCount;
        });
        this.pageSizeOptions.push(dataResult.totalCount);
      },
      error: (err: any) => {
        // this.notification.error(`Có lỗi xảy ra khi lấy dữ liệu`, `${err.message}`);
      },
      complete: () => this.gridApi.hideOverlay()
    });
    return rs;
  }
  //#endregion API Event
}
