<div *ngIf="!this.params.isCheckBox" class="cell-content" nz-row>
  <input
    style="width: 75%"
    [disabled]="lock"
    nz-input
    type="text"
    [(ngModel)]="formattedValue"
    (ngModelChange)="onInputChange($event)"
    [attr.id]="inputId"
    (blur)="onBlur()"
    (focus)="onFocus()"
  />
</div>
<div *ngIf="this.params.isCheckBox" class="cell-content" nz-row>
  <label [disabled]="this.params.data.lock" nz-checkbox [(ngModel)]="value"></label>
</div>
<div *ngIf="this.params.isCheckBox" class="cell-content" nz-row>
  <label [disabled]="this.params.data.lock" nz-checkbox [(ngModel)]="value"></label>
</div>
<style>
  .warp-content span {
    text-align: center;
  }
  .warp-content {
    height: 20px;
  }
  .cell-content {
    display: flex;
    justify-content: center;
    text-align: center;
    align-items: center;
  }
  .cell-content input {
    text-align: center;
  }
  .cell-content input {
    text-align: center;
    border: 1px solid #d9d9d9; /* Màu xám nhạt */
    border-radius: 4px; /* Tùy chỉnh góc bo tròn */
    outline: none; /* Loại bỏ viền khi focus */
  }

  .cell-content input:focus {
    border-color: #40a9ff; /* Màu viền khi focus */
    box-shadow: 0 0 5px rgba(24, 144, 255, 0.5); /* Hiệu ứng ánh sáng khi focus */
  }
</style>
