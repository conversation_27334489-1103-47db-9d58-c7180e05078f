<nz-modal
  [(nzVisible)]="isVisible"
  [nzTitle]="modalTitle"
  [nzContent]="modalContent"
  [nzFooter]="modalFooter"
  nzMaskClosable="false"
  nzWidth="800px"
  (nzOnCancel)="handleCancel()"
>
  <ng-template #modalTitle> {{ tittle }} </ng-template>

  <ng-template #modalContent>
    <form nz-form [formGroup]="form" (ngSubmit)="save()">
      <nz-form-item>
        <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired nzFor="code">Mã</nz-form-label>
        <nz-form-control [nzSm]="17" [nzXs]="24">
          <input nz-input formControlName="code" id="code" placeholder="Nhập vào mã" />
        </nz-form-control>
      </nz-form-item>

      <nz-form-item>
        <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired nzFor="name">Tên</nz-form-label>
        <nz-form-control [nzSm]="17" [nzXs]="24" nzErrorTip="Tên ứng dụng không được để trống!">
          <input nz-input formControlName="name" id="name" placeholder="Nhập vào tên" />
        </nz-form-control>
      </nz-form-item>

      <nz-form-item>
        <nz-form-label [nzSm]="6" [nzXs]="24" nzFor="order">Thứ tự</nz-form-label>
        <nz-form-control [nzSm]="17" [nzXs]="24">
          <input nz-input type="number" id="order" formControlName="order" placeholder="Nhập vào số thứ tự" />
        </nz-form-control>
      </nz-form-item>

      <nz-form-item nz-row>
        <nz-form-label [nzSm]="6" [nzXs]="24" nzFor="status">Trạng thái</nz-form-label>
        <nz-form-control [nzSpan]="17">
          <nz-switch formControlName="status" nzSize="small" style="margin-right: 5px"></nz-switch>
          <label>
            <nz-tag [nzColor]="'#00BB00'" [hidden]="!form.value.status">Đang áp dụng</nz-tag>
            <nz-tag [nzColor]="'#EF5350'" [hidden]="form.value.status">Ngừng áp dụng</nz-tag>
          </label>
        </nz-form-control>
      </nz-form-item>

      <nz-form-item style="margin-bottom: 0">
        <nz-form-label [nzSm]="6" [nzXs]="24" nzFor="description"> Mô tả </nz-form-label>
        <nz-form-control [nzSm]="17" [nzXs]="24">
          <textarea rows="3" nz-input id="description" formControlName="description" placeholder="Nhập vào mô tả"></textarea>
        </nz-form-control>
      </nz-form-item>
    </form>
  </ng-template>

  <ng-template #modalFooter>
    <button
      nz-button
      nzType="primary"
      class="btn-secondary"
      *ngIf="isInfo && btnEdit.visible && btnEdit.grandAccess"
      (click)="btnEdit.click($event)"
    >
      <i nz-icon nzType="edit" nzTheme="fill"></i>{{ btnEdit.title }}
    </button>
    <button
      nz-button
      nzType="primary"
      class="btn-primary"
      *ngIf="!isInfo && btnSave.visible && btnSave.grandAccess"
      [nzLoading]="isLoading"
      (click)="btnSave.click($event)"
    >
      <i nz-icon nzType="save" nzTheme="fill"></i>{{ btnSave.title }}
    </button>
    <button
      nz-button
      nzType="default"
      class="btn-warning"
      *ngIf="btnCancel.visible && btnCancel.grandAccess"
      (click)="btnCancel.click($event)"
    >
      <i nz-icon nzType="close-circle" nzTheme="fill"></i>{{ btnCancel.title }}
    </button>
  </ng-template>
</nz-modal>
