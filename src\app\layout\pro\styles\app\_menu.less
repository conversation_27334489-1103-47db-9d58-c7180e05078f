@{alain-pro-prefix} {
  &__menu {
    display: block;
    &-item {
      &--disabled {
        pointer-events: none;
      }
    }
    &-only-icon {
      @{alain-pro-prefix}__menu-item {
        padding-right: 8px !important;
        padding-left: 8px !important;
        &:first-child {
          padding-left: 0;
        }
      }
      @{alain-pro-prefix}__menu-icon {
        margin-right: 0;
        font-size: @alain-pro-top-nav-only-icon-fs;
      }
    }
    &-title {
      position: relative;
      &-badge {
        display: flex;
        justify-content: center;
        width: 18px;
        height: 18px;
        padding: 0;
        font-size: 12px;
        line-height: 18px;
        background: @alain-pro-header-title-badge-bg;
        border-radius: 50%;
        > em {
          color: @alain-pro-header-title-badge-color;
          font-style: normal;
        }
      }
    }
    &-img {
      width: @alain-pro-sider-menu-img-wh !important;
      height: @alain-pro-sider-menu-img-wh !important;
    }
  }
  &__side-nav {
    @{alain-pro-prefix}__menu {
      &-title {
        display: flex;
        align-items: center;
        &-text {
          flex: 1;
        }
      }
    }
    .@{ant-prefix}-menu-inline-collapsed {
      @{alain-pro-prefix}__menu-title-badge {
        position: absolute;
        top: 0;
        right: -16px;
        width: 8px;
        height: 8px;
        > em {
          display: none;
        }
      }
    }
  }
}
