.fc-scrollgrid-sync-inner{
    padding: 1em 0px;
    background-color: #ebecec;
}
.fc-col-header-cell-cushion{
    font-weight: 600;
    display: flex;
    color: black;
}
.fc .fc-daygrid-day-frame {
    background-color: white;
}
.fc .fc-scroller-liquid-absolute {
    inset: 0px;
    position: absolute;
}
:root {
    --fc-event-bg-color: #fff;
    --fc-event-border-color: #fff;
}

.fc-scroller-harness .fc-scroller{
    scrollbar-width: thin;
    scrollbar-color: rgba(0, 0, 0, 0.2) rgba(0, 0, 0, 0.1);
}
.fc-direction-ltr .fc-daygrid-event.fc-event-end {
    // background-color: #3788d8;
    // color: #fff;
    // overflow-y: auto;
    // scrollbar-width: thin;
    padding: 0px 5px
}
.fc-timegrid-event .fc-event-main {
    padding: 0px; 
}
.fc-direction-ltr .fc-daygrid-event.fc-event-end {
    padding: 0px;
}
.fc-direction-ltr .fc-button-group > .fc-button:not(:first-child) {
    background-color: #fff;
    border: #dddddd solid 1px;
    color: #000;
}
.fc .fc-button-primary {
    background-color: #fff;
    border: #dddddd solid 1px;
    color: #000;
}
.fc-direction-ltr .fc-daygrid-more-link {
    float: left;
    background-color: azure;
    border: 1px solid;
    padding: 5px 10px;
}

.fc-direction-ltr .fc-daygrid-more-link:hover {
    background-color: azure;
}

.fc .fc-toolbar-title {
    font-size: 1.6em;
    margin: 0px;
}
.fc-license-message{
    display: none;
}
