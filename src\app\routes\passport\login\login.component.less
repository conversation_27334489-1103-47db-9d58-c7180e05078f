@import '@delon/theme/index';

:host {
  display: block;
  width: 368px;
  margin: 0 auto;

  ::ng-deep {
    .ant-tabs .ant-tabs-bar {
      margin-bottom: 24px;
      text-align: center;
      border-bottom: 0;
    }

    .forgot{
      position: relative;
      top: 10px;
    }
    
    .form-login {
      padding: 20px;
      padding-left: 30px;
      background-color: #fff;
      border: 1px solid #d9d9d9; 
      border-radius: 12px; 
    } 

    #title-text-two {
      position: relative;
      top: 100%; 
      margin-left: 3.6em;
      color: #0079c2;
      font-size: 24px;
      text-align: center;
    }

    .form-input-login{
      max-width: 96%;
      margin-top: 40px;
      border-radius: 12px;
    }

    .form-input-password{
      max-width: 96%;
      border-radius: 12px;
    }

    .btn-login {
      max-width: 96%;
      background-color: #0082c6;
      border: 1px solid transparent; 
      border-radius: 12px; 
    }

    .check-box-text{
      position: relative;
      top: -25px; 
    }

    .text-center {
  position: relative;
  top: -20px; // Adjust the value as needed to move the text up
  
}

    .ant-tabs-tab {
      margin-bottom: 10px;
      margin-left: 2.6em;
      font-size: 20px;
      line-height: 30px;
    }

    .ant-input-affix-wrapper .ant-input:not(:first-child) {
      padding-left: 4px;
    }

    .icon {
      margin-left: 16px;
      color: rgb(0 0 0 / 20%);
      font-size: 24px;
      vertical-align: middle;
      cursor: pointer;
      transition: color 0.3s;

      &:hover {
        color: @primary-color;
      }
    }
    


    .other {
      position: relative;
      top: 6px;
      margin-top: 10px;
      margin-left: 40px;
      line-height: 22px;
      text-align: left;

      nz-tooltip {
        vertical-align: middle;
      }

      .register {
        float: center;
      }
    }
  }
}

[data-theme='dark'] {
  :host ::ng-deep {
    .icon {
      color: rgb(255 255 255 / 20%);

      &:hover {
        color: #fff;
      }
    }
  }
}
@media (max-width: 375px) {
  .btn-login {
    max-width: 90%;
  }

  .form-input-login{
    max-width: 90%;
  }

  .form-input-password{
    max-width: 90%;
  }
}

