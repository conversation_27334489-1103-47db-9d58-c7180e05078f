import { Component, ElementRef, ViewChild } from '@angular/core';
import { ICellRendererAngularComp } from 'ag-grid-angular';
import { NzUploadFile, NzUploadXHRArgs } from 'ng-zorro-antd/upload';
import { delay, of, Subscription } from 'rxjs';

@Component({
  selector: 'app-file-cell-renderer',
  templateUrl: './file-cell-renderer.component.html'
})
export class FileCellRendererComponent implements ICellRendererAngularComp {
  params: any;
  value: string = '';
  hoveringDownload = false;
  hoveringDelete = false;
  @ViewChild('fileInput', { static: false }) fileInput!: ElementRef;
  agInit(params: any): void {
    this.params = params;
    this.value = params.value;
  }

  refresh(): boolean {
    return false;
  }

  download() {
    if (this.params.downloadClicked) {
      this.params.downloadClicked(this.params.data);
    }
  }
  upload() {
    this.fileInput.nativeElement.click();
  }

  delete() {
    if (this.params.deleteClicked) {
      this.params.deleteClicked(this.params.data);
    }
  }

  handleUpload = (item: NzUploadXHRArgs) => {};
  triggerFileUpload(): void {
    this.fileInput.nativeElement.click();
  }

  onFileSelected(event: any): void {
    const file: File = event.target.files?.[0];
    if (!file) return;

    const nzFile: NzUploadFile = {
      uid: `${Date.now()}`,
      name: file.name,
      status: 'done',
      originFileObj: file,
      size: file.size,
      type: file.type
    };

    this.params?.uploadClicked?.(nzFile, this.params.data);

    // Reset input để lần sau chọn lại file cũ vẫn gọi được change
    this.fileInput.nativeElement.value = '';
  }
}
