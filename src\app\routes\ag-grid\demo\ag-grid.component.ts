import { Component } from '@angular/core';
import { StatusCellRenderComponent } from '@shared';
import { EXCEL_STYLES_DEFAULT } from '@util';

// eslint-disable-next-line import/no-unassigned-import
import 'ag-grid-enterprise';

@Component({
  selector: 'app-ag-grid',
  templateUrl: './ag-grid.component.html',
  styleUrls: ['./ag-grid.component.less']
})
export class AgGridComponent {
  columnDefs = [
    { headerName: '#', field: 'index', width: 70 },
    { field: 'make', headerName: 'Tên', sortable: true, filter: true, editable: true, minWidth: 180, flex: 1 },
    { field: 'status', headerName: 'Trạng thái', minWidth: 150, cellRenderer: 'statusCellRender' },
    {
      field: 'price',
      headerName: 'Giá',
      sortable: true,
      valueFormatter: (params: any) => this.currencyFormatter(params.value, '$ ')
    }
  ];
  rowData = [
    { index: 1, make: 'Toyota', status: 'Đang áp dụng', price: 35000 },
    { index: 2, make: 'Ford', status: 'Ngừng áp dụng', price: 32000 },
    { index: 3, make: 'Porsche', status: 'Đang áp dụng', price: 72000 }
  ];
  gridApi: any;
  gridColumnApi: any;
  components: any;
  excelStyles: any;

  constructor() {
    this.components = {
      statusCellRender: StatusCellRenderComponent
    };
    this.excelStyles = [...EXCEL_STYLES_DEFAULT];
  }

  onGridReady(params: any): void {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
  }

  // DATA FORMATTING
  currencyFormatter(currency: any, sign: string): string {
    const sansDec = currency.toFixed(0);
    const formatted = sansDec.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    return `${sign}${formatted}`;
  }
}
