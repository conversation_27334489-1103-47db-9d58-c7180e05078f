import { Component } from '@angular/core';
import { ICellRendererAngularComp } from 'ag-grid-angular';

@Component({
  selector: 'app-status-input-cell-render',
  templateUrl: './status-input-cell-render.component.html'
})
export class StatusInputCellRenderComponent implements ICellRendererAngularComp {
  inputId: string = '';
  params: any;
  value: number = 0;
  formattedValue: string = '';
  lock: boolean = false;

  agInit(params: any): void {
    this.params = params;
    this.inputId = `input-${this.params.rowIndex + 1}-${this.params.column.getId()}`;
    this.lock = this.params.data.lockDiemThi;

    if (this.params.isDiemThanhPhan) {
      const diem = this.params.data.diems.find((item: any) => item.idDiem === this.params.idDiemThanhPhan);
      this.value = diem?.diem || 0;
    } else {
      this.value = this.params.value || 0;
    }

    this.formattedValue = this.formatNumber(this.value);
  }

  refresh(params: any): boolean {
    this.value = params.value || 0;
    this.formattedValue = this.formatNumber(this.value);
    return true;
  }

  onInputChange(value: string): void {
    // Xóa dấu chấm và cập nhật giá trị thực
    const rawValue = Number(value.replace(/\./g, ''));
    this.value = isNaN(rawValue) ? 0 : rawValue;
    this.formattedValue = value;
    this.params.node.setDataValue(this.params.colDef.field, this.value);
  }

  onBlur(): void {
    // Format lại giá trị khi mất tiêu điểm
    this.formattedValue = this.formatNumber(this.value);
  }

  onFocus(): void {
    // Loại bỏ định dạng khi lấy tiêu điểm
    this.formattedValue = this.value.toString();
  }

  private formatNumber(value: number): string {
    return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, '.');
  }
}
