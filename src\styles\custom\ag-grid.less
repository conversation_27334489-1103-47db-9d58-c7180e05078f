.ag-cell-wrap-text {
    word-break: break-word !important;
}
.ag-body-horizontal-scroll-viewport {
    scrollbar-width: thin;
    scrollbar-color: rgba(0, 0, 0, 0.2) rgba(0, 0, 0, 0.1);
}
.ag-body-vertical-scroll-viewport {
    scrollbar-width: thin;
    scrollbar-color: rgba(0, 0, 0, 0.2) rgba(0, 0, 0, 0.1);
}
.ag-horizontal-left-spacer, .ag-horizontal-right-spacer {
    scrollbar-width: thin;
    scrollbar-color: rgba(0, 0, 0, 0.2) rgba(0, 0, 0, 0.1);
}
// scroll bar back groud color
.cdk-virtual-scrollable {
    scrollbar-width: thin;
    scrollbar-color: rgba(0, 0, 0, 0.2) rgba(0, 0, 0, 0.1);
}
.ag-header-cell-label {
    display: flex !important;
    justify-content: center !important;
    text-align: center;
}

.ag-checkbox-input-wrapper {
    border: gray solid 1px !important;
}