.attachment {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  padding: 4px;
  border: 1px solid @grey-4;
  &-icon {
    padding: 2px;
    font-size: 40px;
  }
  &-img {
    width: 64px;
    height: 64px;
    background-color: transparent;
    background-position: center center;
    background-size: cover;
  }
}

.feed {
  &__icon {
    position: relative;
    &-tag {
      position: absolute;
      top: -6px;
      right: -2px;
    }
  }
}

.img {
  &-thm {
    position: relative;
    display: block;
    max-width: 100%;
    height: auto;
    background-color: transparent;

    &:hover {
      .img-thm__icon {
        opacity: 1;
      }
    }

    &__mask {
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      z-index: 2;
      display: block;
      background-color: rgba(0, 0, 0, 0.3);
      transition: all 0.2s ease-in-out;
    }

    &__icon {
      position: absolute;
      top: 50%;
      left: 50%;
      z-index: 3;
      display: block;
      color: #fff;
      font-size: 32px;
      opacity: 0;
      transition: all 0.2s ease-in-out;
    }

    &__img {
      z-index: 1;
      max-width: 100%;
      height: auto;
    }

    &__zoom-in {
      overflow: hidden;
      .img-thm__icon {
        transform: translate(-50%, -50%) scale(0.6);
      }
      .img-thm__img {
        transition: all 0.3s ease-in-out;
      }
      &:hover {
        .img-thm__icon {
          transform: translate(-50%, -50%) scale(1);
        }
        .img-thm__img {
          transform: scale(1.1);
        }
      }
    }

    &:not(:hover):not(:focus) {
      .img-thm__mask {
        opacity: 0;
      }
    }
  }
}
