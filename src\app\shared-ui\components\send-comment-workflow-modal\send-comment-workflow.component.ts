import { ChangeDetectionStrategy, ChangeDetectorRef, Component, EventEmitter, Inject, Input, OnInit, Output } from '@angular/core';
import { FormGroup, FormBuilder, Validators, AbstractControl, ValidationErrors } from '@angular/forms';
import { I18NService } from '@core';
import { ACLService } from '@delon/acl';
import { ALAIN_I18N_TOKEN } from '@delon/theme';
import { log } from '@delon/util';
import { ButtonModel } from '@model';
import { AuthJWTApiService } from '@shared-service';
import { EVENT_TYPE, cleanForm } from '@util';
import { NzMessageService } from 'ng-zorro-antd/message';

@Component({
  selector: 'shared-send-comment-workflow',
  templateUrl: 'send-comment-workflow.component.html'
})
export class SendCommentWorkflowModalComponent implements OnInit {
  @Input() item: any;
  @Input() isVisible = false;
  @Output() readonly eventEmmit = new EventEmitter<any>();

  isLoading = false;
  data: any;
  commentary: string = '';
  constructor(
    public readonly authApiService: AuthJWTApiService,
    private readonly cdRef: ChangeDetectorRef,
    @Inject(ALAIN_I18N_TOKEN) private readonly i18n: I18NService
  ) {}

  public handleCancel(): void {
    this.isVisible = false;
  }

  ngOnInit(): void {
    this.initRightOfUser();
  }

  initRightOfUser(): void {}

  resetForm(): void {}

  //#endregion Update-form-type

  public initData(data: any): void {
    this.data = data;
    this.isVisible = true;
    this.commentary = '';
    this.cdRef.detectChanges();
  }

  handleOk(): void {
    this.eventEmmit.emit({ ...this.data, commentary: this.commentary });
  }
}
