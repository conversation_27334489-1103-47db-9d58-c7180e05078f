<style>
  .img-button {
    height: 17px;
    margin-right: 5px;
    cursor: pointer;
  }

  .img-button :hover {
    cursor: pointer;
  }
</style>
<button
  nz-button
  nzType="primary"
  *ngIf="params.data.infoGrantAccess"
  nzShape="circle"
  class="btn-info"
  (click)="btnInfoClickedHandler($event)"
  title="Chi tiết"
>
  <i nz-icon nzType="info" nzTheme="outline"></i>
</button>

<!-- <button
  nz-button
  nzType="primary"
  *ngIf="params.data.startWorkflowGrantAccess"
  nzShape="circle"
  class="btn-info"
  (click)="btnStartWorkflowClickedHandler($event)"
  title="Gửi yêu cầu phê duyệt"
>
  <i nz-icon nzType="send" nzTheme="outline"></i>
</button> -->

<button
  nz-button
  nzType="primary"
  nzShape="circle"
  class="btn-secondary"
  *ngIf="params.data.editGrantAccess"
  (click)="btnEditClickedHandler($event)"
  title="Cập nhật"
>
  <i nz-icon nzType="edit" nzTheme="outline"></i>
</button>

<button
  nz-button
  nzType="primary"
  nzShape="circle"
  class="btn-secondary"
  *ngIf="params.data.thamDinhGrantAccess"
  (click)="btnThamDinhClickedHandler($event)"
  title="Nhận xét thẩm định"
>
  <i nz-icon nzType="message" nzTheme="outline"></i>
</button>

<button
  nz-button
  class="button-success"
  nzType="default"
  nzShape="circle"
  *ngIf="params.data.nghiemThuGrantAccess"
  (click)="btnNghiemThuClickedHandler($event)"
  title="Nhận xét nghiệm thu"
>
  <i nz-icon nzType="check-circle" nzTheme="outline"></i>
</button>

<button
  nz-button
  nzType="primary"
  *ngIf="params.data.processWorkflowGrantAccess"
  nzShape="circle"
  class="btn-info"
  (click)="onShowPopover()"
  title="Xử lý quy trình"
  nz-popover
  [nzPopoverTitle]="titleTemplate"
  [(nzPopoverVisible)]="visibleProcess"
  nzPopoverTrigger="click"
  [nzPopoverContent]="contentTemplate"
>
  <i nz-icon nzType="select" nzTheme="outline"></i>
</button>
<ng-template #titleTemplate>
  Xử lý quy trình
  <button
    nz-button
    nzType="text"
    class="btn-close"
    (click)="visibleProcess = false"
    style="float: right; margin-top: -5px; margin-right: -5px"
    ><span nz-icon nzType="close"></span
  ></button>
</ng-template>
<ng-template #contentTemplate>
  <!-- Container chứa các nút -->
  <button
    *ngFor="let item of params.data.commands"
    nz-button
    nzType="primary"
    nzSize="small"
    class="button-info"
    (click)="btnProcessWorkflowClickedHandler(item.key)"
    [title]="item.value"
  >
    {{ item.value }}
  </button>
</ng-template>

<button
  nz-button
  nzType="primary"
  *ngIf="params.data.viewHistoryWorkflowGrantAccess"
  nzShape="circle"
  class="btn-info"
  (click)="btnViewHistoryWorkflowClickedHandler($event)"
  title="Xem lịch sử phê duyệt"
>
  <i nz-icon nzType="history" nzTheme="outline"></i>
</button>

<button
  nz-button
  nzType="primary"
  nzShape="circle"
  class="button-success"
  *ngIf="params.data.activeGrantAccess"
  (click)="btnActiveClickedHandler($event)"
  title="Sử dụng"
>
  <i nz-icon nzType="play-circle" nzTheme="outline"></i>
</button>

<button
  nz-button
  nzType="primary"
  nzShape="circle"
  nzDanger
  *ngIf="params.data.deleteGrantAccess"
  (click)="btnDeleteClickedHandler($event)"
  title="Xóa"
>
  <i nz-icon nzType="delete" nzTheme="outline"></i>
</button>
<button
  nz-button
  class="button-success"
  nzType="default"
  nzShape="circle"
  nz-tooltip="In"
  *ngIf="params.data.printGrantAccess"
  (click)="btnPrintClickedHandler($event)"
>
  <i nz-icon nzType="printer" nzTheme="outline"></i>
</button>

<button
  nz-button
  class="button-success"
  nzType="default"
  nzShape="circle"
  nz-tooltip="Upload"
  *ngIf="params.data.uploadGrantAccess"
  (click)="triggerFileUpload()"
>
  <i nz-icon nzType="upload" nzTheme="outline"></i>
</button>

<input type="file" #fileInput hidden (change)="onFileSelected($event)" />

<button
  nz-button
  nzType="primary"
  nzShape="circle"
  nzDanger
  *ngIf="params.data.sendMailForgotPasswordGrantAccess"
  (click)="btnSendMailForgotPasswordClickedHandler($event)"
  title="Gửi mail"
>
  <i nz-icon nzType="mail" nzTheme="outline"></i>
</button>

<button
  nz-button
  nzType="primary"
  nzShape="circle"
  class="btn-secondary"
  *ngIf="params.data.addUserRoleGrantAccess"
  (click)="btnAddUserRoleClickedHandler($event)"
  title="Phân quyền nhóm người dùng"
>
  <i nz-icon nzType="idcard" nzTheme="outline"></i>
</button>

<!-- <img
  class="img-button"
  src="../../../../../assets/tmp/icon/user-role.svg"
  *ngIf="params.data.addUserRoleGrantAccess"
  (click)="btnAddUserRoleClickedHandler($event)"
  alt="Phân quyền nhóm người dùng"
  title="Phân quyền nhóm người dùng"
/> -->

<button
  nz-button
  nzType="primary"
  nzShape="circle"
  class="btn-secondary"
  *ngIf="params.data.userGroupAddGrantAccess"
  (click)="btnUpdateUserRole($event)"
  title="Phân quyền người dùng"
>
  <i nz-icon nzType="usergroup-add" nzTheme="outline"></i>
</button>

<!-- <img
  class="img-button"
  src="../../../../../assets/tmp/icon/user-grant.svg"
  *ngIf="params.data.userGroupAddGrantAccess"
  (click)="btnUpdateUserRole($event)"
  alt="Danh sách người dùng"
  title="Phân quyền người dùng"
/> -->

<button
  nz-button
  nzType="primary"
  nzShape="circle"
  class="btn-secondary"
  *ngIf="params.data.addPermissionGrantAccess"
  (click)="btnAddPermissionClickedHandler($event)"
  title="Phân quyền chức năng"
>
  <i nz-icon nzType="partition" nzTheme="outline"></i>
</button>

<!-- <img
  class="img-button"
  src="../../../../../assets/tmp/icon/right.svg"
  *ngIf="params.data.addPermissionGrantAccess"
  (click)="btnAddPermissionClickedHandler($event)"
  alt="Phân quyền chức năng"
  title="Phân quyền chức năng"
/> -->
<style>
  .button-info {
    color: #fff;
    background-color: #17a2b8;
    border-color: #17a2b8;
  }

  .button-info:hover {
    color: #fff;
    background-color: #138496;
    border-color: #117a8b;
  }
</style>
