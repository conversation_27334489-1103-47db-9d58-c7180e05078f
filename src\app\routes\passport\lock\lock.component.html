<div class="ant-card width-lg" style="margin: 0 auto">
  <div class="ant-card-body">
    <div class="avatar">
      <nz-avatar [nzSrc]="user.avatar" nzIcon="user" nzSize="large"></nz-avatar>
    </div>
    <form nz-form [formGroup]="f" (ngSubmit)="submit()" role="form" class="mt-md">
      <nz-form-item>
        <nz-form-control [nzErrorTip]="'validation.password.required' | i18n">
          <nz-input-group nzSuffixIcon="lock">
            <input type="password" nz-input formControlName="password" />
          </nz-input-group>
        </nz-form-control>
      </nz-form-item>
      <nz-row nzType="flex" nzAlign="middle">
        <nz-col [nzOffset]="12" [nzSpan]="12" style="text-align: right">
          <button nz-button [disabled]="!f.valid" nzType="primary">{{ 'app.lock' | i18n }}</button>
        </nz-col>
      </nz-row>
    </form>
  </div>
</div>
