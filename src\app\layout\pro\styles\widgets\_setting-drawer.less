@{setting-drawer-prefix} {
  &__handle-opened {
    right: 300px !important;
  }

  &__blockChecbox {
    display: flex;
    &-item {
      position: relative;
      margin-right: 16px;
      // box-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.1);
      border-radius: @border-radius-base;
      cursor: pointer;
      img {
        width: 48px;
      }
    }
    &-selectIcon {
      position: absolute;
      top: 0;
      right: 0;
      width: 100%;
      height: 100%;
      padding-top: 15px;
      padding-left: 24px;
      color: @primary-color;
      font-weight: bold;
      font-size: 14px;
    }
  }

  &__handle {
    top: 114px;
    width: 32px;
    height: 32px;
    background: rgba(0, 0, 0, 0.38);
    &-icon {
      font-size: 16px;
    }
  }
}

.layout-pro-setting-drawer-rtl-mixin(@enabled) when(@enabled=true) {
  @{setting-drawer-prefix}-rtl {
    @{setting-drawer-prefix} {
      &__handle-opened {
        right: inherit !important;
        left: 300px !important;
      }
    }
  }
}
.layout-pro-setting-drawer-rtl-mixin(@rtl-enabled);
