@chat-prefix: ~'.chat';

@{chat-prefix} {
  &__container {
    display: flex;
    overflow: hidden;
    background: @chat-background;
    border: 1px solid @border-color-split;
  }
  &__scroll {
    &-container {
      position: relative;
      width: 100%;
    }
  }
  &__sidebox {
    display: flex;
    flex-direction: column;
    width: @chat-sidebox-width;
    box-shadow: @shadow-1-right;
    transition: 0.2s all;
  }
  &__contact {
    display: flex;
    align-items: center;
    padding: 8px 16px;
    color: #4e5155;
    cursor: pointer;
    &:hover:not(.chat__contact-active) {
      background-color: rgba(24, 28, 33, 0.03);
    }
    .ant-badge-status-text {
      color: #52c41a;
      font-size: 12px;
    }
    &-offline {
      opacity: 0.7;
      .ant-badge-status-text {
        color: #f5222d;
      }
    }
    &-active {
      background-color: #e84c64;
      &,
      .ant-badge-status-text {
        color: #fff;
      }
    }
  }
  &__user {
    &-avatar {
      height: 40px;
      border-radius: 50%;
    }
  }
  &__message {
    &-container {
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 100%;
      padding: 24px;
    }

    display: flex;
    flex-shrink: 0;
    margin-bottom: 8px;
    &-left {
      margin-right: @chat-avatar-box-width + 8px;
      .chat__message-avatar {
        margin-right: 8px;
      }
    }
    &-right {
      flex-direction: row-reverse;
      margin-left: @chat-avatar-box-width + 8px;
      .chat__message-avatar {
        margin-left: 8px;
      }
    }
    &-send {
      text-align: center;
    }
    &-avatar {
      width: @chat-avatar-box-width;
      text-align: center;
    }
    &-text {
      width: 100%;
      color: @grey-6;
      font-size: 12px;
      text-align: center;
    }
    &-time {
      padding-top: 2px;
      color: @grey-6;
      font-size: 12px;
      white-space: nowrap;
    }
    &-msg {
      padding: 4px 8px;
      background: @chat-message-item-bg;
      border-radius: 4px;
      &--name {
        display: block;
        margin-bottom: 2px;
      }
    }
  }
}

.chat-rtl-mixin(@enabled) when(@enabled=true) {
  [dir='rtl'] {
    @{chat-prefix} {
      &__message {
        &-left {
          margin-right: inherit;
          margin-left: @chat-avatar-box-width + 8px;
          .chat__message-avatar {
            margin-right: inherit;
            margin-left: 8px;
          }
        }
        &-right {
          margin-right: inherit;
          margin-left: @chat-avatar-box-width + 8px;
          .chat__message-avatar {
            margin-right: inherit;
            margin-left: 8px;
          }
        }
      }
    }
  }
}
.chat-rtl-mixin(@rtl-enabled);
