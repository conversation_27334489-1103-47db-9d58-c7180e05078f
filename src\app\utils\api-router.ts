export const applicationRouter = {
  create: `/api/v1/system-application`,
  createMany: `/api/v1/system-application/create-many`,
  update: `/api/v1/system-application`,
  delete: `/api/v1/system-application`,
  getById: `/api/v1/system-application/`,
  getFilter: `/api/v1/system-application/filter`,
  getAll: `/api/v1/system-application/all`,
  getCombobox: `/api/v1/system-application/for-combobox`
};
export const permissionRouter = {
  getListCombobox: `/system/v1/permission/for-combobox`
};

export const phanHeRouter = {
  getAuthorizedForUser: `/system/v1/phan-he/authorized-for-users`,
  getListCombobox: `/system/v1/phan-he/for-combobox`
};

export const heDaoTaoRouter = {
  getCombobox: '/system/v1/he/for-combobox'
};

export const fileRouter = {
  uploadObject: `/system/v1/file/upload-object`,
  downloadFile: `/system/v1/file/download-base64/`
};
