.button-info {
    color: #fff;
    background-color: #17a2b8;
    border-color: #17a2b8;
}

.button-info:hover {
    color: #fff;
    background-color: #138496;
    border-color: #117a8b;
}
  
.button-danger{
    color: #fff;
    background-color: #FF4D4F;
    border-color: #FF4D4F; 
}

.button-danger:hover {
    color: #fff;
    background-color: #c52426;
    border-color: #c52426;
}

.button-add{
    background-color: #17a2b8 
}

.button-success {
    color: #fff;
    background-color: #28a745;
    border-color: #28a745;
}

.button-success:hover {
    color: #fff;
    background-color: #218838;
    border-color: #1e7e34;
}

.button-warning {
    color: #fff;
    background-color: #FF7F00;
    border-color: #FF7F00;
}

.button-warning:hover {
    color: #fff;
    background-color: #FF9A33;
    border-color: #FF9A33;
}

.button-item {
    text-align: left;
}

.icon-danger {
    color: #FF4D4F;
}

.icon-success {
    color: #28a745;
}

.icon-warning {
    color: #FF7F00;
}