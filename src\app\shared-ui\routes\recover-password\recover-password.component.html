<div class="background">
  <form nz-form [formGroup]="form" class="form-login" (ngSubmit)="submit()" role="form">
    <nz-form-item>
      <nz-form-control [nzSpan]="24" [nzErrorTip]="'password-login.required' | i18n" class="title-login text-center">
        <span id="title-text-two">{{ 'Cập nhật mật khẩu' | i18n }}</span>
      </nz-form-control>
    </nz-form-item>
    <nz-form-item>
      <nz-form-control *ngIf="success || error" [nzSpan]="24" [nzErrorTip]="'password-login.required' | i18n">
        <nz-alert *ngIf="success" [nzType]="'success'" [nzMessage]="success" [nzShowIcon]="true" class="mb-lg"></nz-alert>
        <nz-alert *ngIf="error" [nzType]="'error'" [nzMessage]="error" [nzShowIcon]="true" class="mb-lg"></nz-alert>
      </nz-form-control>
    </nz-form-item>
    <nz-form-item *ngIf="!success">
      <nz-form-control [nzSpan]="24" [nzErrorTip]="'password-change.required' | i18n">
        <nz-input-group class="form-input-password" nzSize="large" nzPrefixIcon="lock" [nzSuffix]="suffixIconShowHide">
          <input nz-input [type]="typePassword" formControlName="newPassword" placeholder="{{ 'password.placeholder' | i18n }}" />
        </nz-input-group>
        <ng-template #suffixIconShowHide>
          <span (click)="switchShowPass()" *ngIf="!showPassword"><i nz-icon nzType="eye" nzTheme="outline"></i></span>
          <span (click)="switchShowPass()" *ngIf="showPassword"><i nz-icon nzType="eye-invisible" nzTheme="outline"></i></span>
        </ng-template>
      </nz-form-control>
    </nz-form-item>
    <nz-form-item *ngIf="!success">
      <nz-form-control [nzSpan]="24" [nzErrorTip]="'password-change-confirm.required' | i18n">
        <nz-input-group class="form-input-password" nzSize="large" nzPrefixIcon="lock" [nzSuffix]="suffixIconShowHide1">
          <input
            nz-input
            [type]="typePassword1"
            formControlName="confirmNewPassword"
            placeholder="{{ 'password-confirm.placeholder' | i18n }}"
          />
        </nz-input-group>
        <ng-template #suffixIconShowHide1>
          <span (click)="switchShowPass1()" *ngIf="!showPassword1"><i nz-icon nzType="eye" nzTheme="outline"></i></span>
          <span (click)="switchShowPass1()" *ngIf="showPassword1"><i nz-icon nzType="eye-invisible" nzTheme="outline"></i></span>
        </ng-template>
      </nz-form-control>
    </nz-form-item>
    <nz-form-item *ngIf="!success">
      <nz-form-control [nzSpan]="24">
        <button nz-button type="submit" nzType="primary" nzSize="large" [nzLoading]="isLoading" nzBlock class="btn-login">
          {{ 'Cập nhật mật khẩu' | i18n }}
        </button>
      </nz-form-control>
    </nz-form-item>
  </form>
</div>
