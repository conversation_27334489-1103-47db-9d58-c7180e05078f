.quill-editor {
  line-height: initial;
  &.ng-dirty.ng-invalid {
    .ql-toolbar,
    .ql-container {
      border-color: @error-color;
    }
  }
}

@ql-sizes: '10px', '12px', '14px', '16px', '18px', '20px', '24px';

.ql-snow {
  .for(@ql-sizes, {
    .ql-size .ql-picker-label,
    .ql-size .ql-picker-item {
      &[data-value="@{adItem}"]::before {
        content: '@{adItem}';
      }
    }
  });
}
