import { CommonModule } from '@angular/common';
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { LangsModule } from '@shared';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { NzDropDownModule } from 'ng-zorro-antd/dropdown';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzMenuModule } from 'ng-zorro-antd/menu';
import { NzPopoverModule } from 'ng-zorro-antd/popover';
import { NzTagModule } from 'ng-zorro-antd/tag';
import { NzUploadModule } from 'ng-zorro-antd/upload';
import { WorkflowStatusCellRenderComponent } from 'src/app/shared-ui/ag-grid/cell-render/workflow-status-cell-render/workflow-status-cell-render.component';

import { BangCongChiTietCellRenderComponent } from './cell-render/bang-cong-chi-tiet-cell-render/bang-cong-chi-tiet-cell-render.component';
import { BangCongGiangVienCellRenderComponent } from './cell-render/bang-cong-giang-vien-cell-render/bang-cong-giang-vien-cell-render.component';
import { BtnCellRenderComponent } from './cell-render/btn-cell-render/btn-cell-render.component';
import { BtnDynamicCellRenderComponent } from './cell-render/btn-dynamic-cell-render/btn-dynamic-cell-render.component';
import { CellVerticalRenderComponent } from './cell-render/cell-vertical-render/cell-vertical-render.component';
import { ChiTietSinhVienDetailCellRenderComponent } from './cell-render/chi-tiet-sinh-vien-detail-cell-render/chi-tiet-sinh-vien-detail-cell-render.component';
import { ChiTietSinhVienDetailDiHocCellRenderComponent } from './cell-render/chi-tiet-sinh-vien-detail-di-hoc-cell-render/chi-tiet-sinh-vien-detail-di-hoc-cell-render.component';
import { DateCellRenderComponent } from './cell-render/date-cell-render/date-cell-render.component';
import { DiemDanhTheoNgayCellRenderComponent } from './cell-render/diem-anh-theo-ngay-detail-cell-render/diem-anh-theo-ngay-detail-cell-render.component';
import { DiemDanhSinhVienCellRenderComponent } from './cell-render/diem-danh-sinh-vien-cell-render/diem-danh-sinh-vien-cell-render.component';
import { FileCellRendererComponent } from './cell-render/file-cell-renderer/file-cell-renderer.component';
import { PhanCaLamViecCellRenderComponent } from './cell-render/phan-ca-lam-viec-cell-render/phan-ca-lam-viec-cell-render.component';
import { SendMailStatusCellRenderComponent } from './cell-render/send-mail-status-cell-render/send-mail-status-cell-render.component';
import { StatusCellRenderComponent } from './cell-render/status-cell-render/status-cell-render.component';
import { StatusCheckBoxCellRenderComponent } from './cell-render/status-check-box-cell-render/status-check-box-cell-render.component';
import { StatusInputCellRenderComponent } from './cell-render/status-input-cell-render/status-input-cell-render.component';
import { WorkflowCellRenderComponent } from './cell-render/workflow-cell-render/workflow-cell-render.component';
import { CustomHeaderRenderComponent } from './header-render/custom-header-render/custom-header-render.component';

const COMPONENTS = [
  BtnCellRenderComponent,
  FileCellRendererComponent,
  StatusCellRenderComponent,
  CustomHeaderRenderComponent,
  PhanCaLamViecCellRenderComponent,
  CellVerticalRenderComponent,
  BangCongChiTietCellRenderComponent,
  BangCongGiangVienCellRenderComponent,
  DateCellRenderComponent,
  StatusCheckBoxCellRenderComponent,
  SendMailStatusCellRenderComponent,
  StatusInputCellRenderComponent,
  BtnDynamicCellRenderComponent,
  DiemDanhTheoNgayCellRenderComponent,
  ChiTietSinhVienDetailCellRenderComponent,
  ChiTietSinhVienDetailDiHocCellRenderComponent,
  DiemDanhSinhVienCellRenderComponent,
  WorkflowCellRenderComponent,
  WorkflowStatusCellRenderComponent
];

@NgModule({
  imports: [
    CommonModule,
    NzDropDownModule,
    NzIconModule,
    NzTagModule,
    NzCheckboxModule,
    LangsModule,
    NzButtonModule,
    FormsModule,
    NzMenuModule,
    NzPopoverModule,
    NzIconModule,
    NzUploadModule
  ],
  declarations: COMPONENTS,
  exports: COMPONENTS
})
export class AggridCellRenderModule {}
