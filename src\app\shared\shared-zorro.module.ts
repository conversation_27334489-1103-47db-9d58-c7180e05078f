import { NzAnchorModule } from 'ng-zorro-antd/anchor';
import { NzAvatarModule } from 'ng-zorro-antd/avatar';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { NzDropDownModule } from 'ng-zorro-antd/dropdown';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzGridModule } from 'ng-zorro-antd/grid';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzListModule } from 'ng-zorro-antd/list';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzPopconfirmModule } from 'ng-zorro-antd/popconfirm';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzSwitchModule } from 'ng-zorro-antd/switch';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzTagModule } from 'ng-zorro-antd/tag';
import { NzTransferModule } from 'ng-zorro-antd/transfer';
import { NzTreeModule } from 'ng-zorro-antd/tree';
import { NzTreeSelectModule } from 'ng-zorro-antd/tree-select';
import { NzTreeViewModule } from 'ng-zorro-antd/tree-view';
import { NzUploadModule } from 'ng-zorro-antd/upload';

export const SHARED_ZORRO_MODULES = [
  NzButtonModule,
  NzGridModule,
  NzTableModule,
  NzFormModule,
  NzInputModule,
  NzTagModule,
  NzModalModule,
  NzUploadModule,
  NzDropDownModule,
  NzCardModule,
  NzSelectModule,
  NzIconModule,
  NzSwitchModule,
  NzDatePickerModule,
  NzTreeViewModule,
  NzTreeSelectModule,
  NzTreeModule,
  NzPopconfirmModule,
  NzTransferModule,
  NzAnchorModule,
  NzCheckboxModule,
  NzAvatarModule,
  NzListModule
];
