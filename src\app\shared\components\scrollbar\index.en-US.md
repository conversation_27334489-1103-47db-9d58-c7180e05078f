---
order: 40
title: scrollbar
type: Component
---

Based on [perfect-scrollbar](http://utatti.github.io/perfect-scrollbar/) perfect custom scrollbar plugin, [DEMO](https://preview.ng-alain.com/pro/#/other/chat).

## API

| Property          | Description              | Type                | Default |
| ----------------- | ------------------------ | ------------------- | ------- |
| `[options]` | [Options](https://github.com/utatti/perfect-scrollbar#options) | `ScrollbarOptions`  | - |
| `[disabled]`      | Whether to disable       | `boolean`           | `false` |
| `[psScrollX]`     | `ps-scroll-x` event      | `EventEmitter<any>` | -       |
| `[psScrollY]`     | `ps-scroll-y` event      | `EventEmitter<any>` | -       |
| `[psScrollUp]`    | `ps-scroll-up` event     | `EventEmitter<any>` | -       |
| `[psScrollDown]`  | `ps-scroll-down` event   | `EventEmitter<any>` | -       |
| `[psScrollLeft]`  | `ps-scroll-left` event   | `EventEmitter<any>` | -       |
| `[psScrollRight]` | `ps-scroll-right` event  | `EventEmitter<any>` | -       |
| `[psXReachStart]` | `ps-x-reach-start` event | `EventEmitter<any>` | -       |
| `[psXReachEnd]`   | `ps-x-reach-end` event   | `EventEmitter<any>` | -       |
| `[psYReachStart]` | `ps-y-reach-start` event | `EventEmitter<any>` | -       |
| `[psYReachEnd]`   | `ps-y-reach-end` event   | `EventEmitter<any>` | -       |
