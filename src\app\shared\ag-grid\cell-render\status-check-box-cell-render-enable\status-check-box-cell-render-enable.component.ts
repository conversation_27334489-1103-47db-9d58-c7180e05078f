import { Component } from '@angular/core';
import { ICellRendererAngularComp } from 'ag-grid-angular';
import { IAfterGuiAttachedParams } from 'ag-grid-community';

@Component({
  selector: 'app-status-check-box-cell-render-enable',
  templateUrl: './status-check-box-cell-render-enable.component.html',
  styleUrls: []
})
export class StatusCheckBoxCellRenderEnableComponent implements ICellRendererAngularComp {
  constructor() {}
  isChecked: boolean = false;

  params: any;

  refresh(params: any): boolean {
    this.isChecked = this.parseBoolean(params.value);
    return true;
  }

  afterGuiAttached?(params?: IAfterGuiAttachedParams): void {
    throw new Error('Method not implemented.');
  }

  agInit(params: any): void {
    this.params = params;
    this.isChecked = this.parseBoolean(params.value);
  }

  onCheckboxChange(event: Event): void {
    const isChecked = (event.target as HTMLInputElement).checked;
    this.params.node.setDataValue(this.params.colDef.field, isChecked);
  }
  private parseBoolean(value: any): boolean {
    return value === true || value === 'true';
  }
}
