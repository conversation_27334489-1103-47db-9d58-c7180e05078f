<div nz-dropdown [nzDropdownMenu]="userMenu" nzPlacement="bottomRight" class="alain-pro__header-item">
  <nz-avatar [nzSrc]="settings.user.avatar" nzSize="small" class="mr-sm"></nz-avatar>
  {{ _user['fullName'] }}
</div>
<nz-dropdown-menu #userMenu="nzDropdownMenu">
  <div nz-menu class="width-sm">
    <div nz-menu-item (click)="openUpdateUserInfo()">
      <i nz-icon nzType="user" class="mr-sm"></i>
      {{ 'menu.account.center' | i18n }}
    </div>
    <div nz-menu-item (click)="openChangePassword()">
      <i nz-icon nzType="key" class="mr-sm"></i>
      {{ 'menu.account.change-password' | i18n }}
    </div>
    <div nz-menu-item *ngIf="canClearCache" (click)="removeAllCache()">
      <i nz-icon nzType="reload" class="mr-sm"></i>
      {{ 'menu.account.reset-cache' | i18n }}
    </div>
    <!-- <div nz-menu-item routerLink="/pro/account/settings">
      <i nz-icon nzType="setting" class="mr-sm"></i>
      {{ 'menu.account.settings' | i18n }}
    </div>
    <div nz-menu-item routerLink="/exception/trigger">
      <i nz-icon nzType="close-circle" class="mr-sm"></i>
      {{ 'menu.account.trigger' | i18n }}
    </div> -->
    <li nz-menu-divider></li>
    <div nz-menu-item (click)="logout()">
      <i nz-icon nzType="logout" class="mr-sm"></i>
      {{ 'menu.account.logout' | i18n }}
    </div>
  </div>
</nz-dropdown-menu>

<shared-change-password #itemModal [isVisible]="modal.isShow" [item]="modal.item" [type]="modal.type" [option]="modal.option">
</shared-change-password>

<shared-update-user-info
  #itemUdUserInfoModal
  [isVisible]="modalUpdateUserInfo.isShow"
  [item]="modalUpdateUserInfo.item"
  [type]="modalUpdateUserInfo.type"
  [option]="modalUpdateUserInfo.option"
>
</shared-update-user-info>
