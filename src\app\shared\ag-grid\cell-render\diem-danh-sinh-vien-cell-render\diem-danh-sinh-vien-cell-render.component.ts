import { Component } from '@angular/core';
import { ICellRendererAngularComp } from 'ag-grid-angular';
import { IAfterGuiAttachedParams } from 'ag-grid-community';

@Component({
  selector: 'diem-danh-sinh-vien-cell-render',
  templateUrl: './diem-danh-sinh-vien-cell-render.component.html',
  styleUrls: []
})
export class DiemDanhSinhVienCellRenderComponent implements ICellRendererAngularComp {
  constructor() {}
  public value: number | undefined;
  public color: string | undefined;
  params: any;

  afterGuiAttached?(params?: IAfterGuiAttachedParams): void {
    throw new Error('Method not implemented.');
  }

  agInit(params: any): void {
    this.params = params;
    this.value = params.value;
    this.setColor(params);
  }

  refresh(params: any): boolean {
    this.params = params;
    this.value = params.value;
    this.setColor(params);
    return true;
  }

  private setColor(params: any): void {
    const item = params.colDef.headerComponentParams;
    const attendance = params.data.dsDiemDanhChiTiet.find(
      (x: any) => x.ngayThang === item.ngayThang && x.tuTiet === item.tuTiet && x.denTiet === item.denTiet
    );
    this.color = attendance && attendance.nghiHoc === true ? 'red' : 'blue';
  }
}
