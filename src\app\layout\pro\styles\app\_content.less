@{alain-pro-prefix} {
  &__main {
    min-height: 100vh;
    background-color: @alain-pro-main-bg;

    > .ant-layout-header {
      height: @alain-pro-header-height;
      line-height: @alain-pro-header-height;
    }

    .router-ant();
  }

  &__page {
    &-header {
      &-wrapper {
        position: relative;
        display: block;
        margin: -@alain-pro-content-margin -@alain-pro-content-margin 0;
      }
      &-content {
        margin: @alain-pro-content-margin @alain-pro-content-margin 0;
      }
    }
    &-grid {
      display: block;
      width: 100%;
      height: 100%;
      min-height: 100%;
      transition: 0.3s;
      &-wide {
        max-width: @alain-pro-wide;
        margin: 0 auto;
      }
      &-no-spacing {
        width: initial;
        margin: -@alain-pro-content-margin -@alain-pro-content-margin 0;
      }
    }
  }
}

@{alain-pro-prefix}__fetching {
  &-icon {
    display: none;
  }
  & {
    @{alain-pro-prefix}__fetching-icon {
      display: block;
    }
  }
}

@media screen and (max-width: @mobile-max) {
  @{alain-pro-prefix}__page-header-content {
    margin: @alain-pro-content-margin 10px 0;
  }
}
