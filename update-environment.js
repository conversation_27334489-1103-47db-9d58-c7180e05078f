// update-environment.js
const fs = require('fs');
const path = require('path');

const environmentFilePath = path.join(__dirname, 'src/environments/environment.prod.ts');
const buildFilePath = path.join(__dirname, 'src/assets/build.json');
const currentDate = Math.floor(new Date().getTime() / 1000);

fs.readFile(environmentFilePath, 'utf8', (err, data) => {
  if (err) {
    return console.log(err);
  }

  let result = data.replace(/build: '.*'/, `build: '${currentDate}'`);

  fs.writeFile(environmentFilePath, result, 'utf8', (err) => {
    if (err) return console.log(err);
  });
});

// Kiểm tra xem file có tồn tại không
if (fs.existsSync(buildFilePath)) {
  // Nếu file tồn tại, đọc và cập nhật file
  fs.readFile(buildFilePath, 'utf8', (err, data) => {
      if (err) {
          console.error('Error reading the file:', err);
          return;
      }

      const jsonData = JSON.parse(data);
      jsonData.build = currentDate;

      const updatedJsonData = JSON.stringify(jsonData, null, 2);

      fs.writeFile(buildFilePath, updatedJsonData, 'utf8', (err) => {
          if (err) {
              console.error('Error writing to the file:', err);
              return;
          }
      });
  });
} else {
  // Nếu file không tồn tại, tạo mới file với thời gian hiện tại
  const newJsonData = {
      build: currentDate
  };

  const jsonDataToWrite = JSON.stringify(newJsonData, null, 2);

  fs.writeFile(buildFilePath, jsonDataToWrite, 'utf8', (err) => {
      if (err) {
          console.error('Error writing to the file:', err);
          return;
      }
  });
}