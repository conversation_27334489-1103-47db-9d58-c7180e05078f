import { Component, EventEmitter, Inject, Input, OnInit, Output } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { I18NService } from '@core';
import { ACLService } from '@delon/acl';
import { ALAIN_I18N_TOKEN } from '@delon/theme';
import { ButtonModel } from '@model';
import { ApplicationApiService } from '@service';
import { cleanForm, EVENT_TYPE, FORM_TYPE } from '@util';
import { NzMessageService } from 'ng-zorro-antd/message';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-application-item',
  templateUrl: './application-item.component.html',
  styleUrls: ['./application-item.component.less']
})
export class ApplicationItemComponent implements OnInit {
  @Input() type = 'add';
  @Input() item: any;
  @Input() isVisible = false;
  @Input() option: any;
  @Output() readonly eventEmmit = new EventEmitter<any>();

  form: FormGroup;
  moduleName = 'ứng dụng';

  isInfo = false;
  isEdit = false;
  isAdd = false;
  tittle = '';

  isLoading = false;
  isReloadGrid = false;

  btnSave: ButtonModel;
  btnCancel: ButtonModel;
  btnEdit: ButtonModel;

  constructor(
    private fb: FormBuilder,
    private messageService: NzMessageService,
    private applicationApiService: ApplicationApiService,
    @Inject(ALAIN_I18N_TOKEN) private i18n: I18NService,
    private aclService: ACLService
  ) {
    this.btnSave = {
      title: this.i18n.fanyi('app.common.button.save'),
      visible: true,
      enable: true,
      grandAccess: true,
      click: ($event: any) => {
        this.save();
      }
    };
    this.btnCancel = {
      title: this.i18n.fanyi('app.common.button.close'),
      visible: true,
      enable: true,
      grandAccess: true,
      click: ($event: any) => {
        this.handleCancel();
      }
    };
    this.btnEdit = {
      title: 'Cập nhật',
      visible: true,
      enable: true,
      grandAccess: true,
      click: ($event: any) => {
        this.updateFormToEdit();
      }
    };
    this.form = this.fb.group({
      code: [null, [Validators.required]],
      name: [null, [Validators.required]],
      order: [null],
      status: [true],
      description: [null]
    });
  }

  handleCancel(): void {
    this.isVisible = false;
    if (this.isReloadGrid) {
      this.eventEmmit.emit({ type: EVENT_TYPE.SUCCESS });
    } else {
      this.eventEmmit.emit({ type: EVENT_TYPE.CLOSE });
    }
  }

  ngOnInit(): void {
    this.initRightOfUser();
  }

  initRightOfUser(): void {
    // this.btnSave.grandAccess = this.aclService.canAbility('APP-CREATE');
    // this.btnEdit.grandAccess = this.aclService.canAbility('APP-EDIT');
  }

  //#region Update-form-type
  updateFormToAdd(): void {
    this.isInfo = false;
    this.isEdit = false;
    this.isAdd = true;
    this.tittle = `Thêm mới ${this.moduleName}`;
    this.item = {};
    this.form.get('code')?.enable();
    this.form.get('code')?.setValue('');
    this.form.get('name')?.enable();
    this.form.get('name')?.setValue('');
    this.form.get('order')?.enable();
    this.form.get('order')?.setValue(0);
    this.form.get('status')?.enable();
    this.form.get('status')?.setValue(true);
    this.form.get('description')?.enable();
    this.form.get('description')?.setValue('');
  }
  updateFormToEdit(): void {
    this.isInfo = false;
    this.isEdit = true;
    this.isAdd = false;
    this.tittle = `Cập nhật ${this.moduleName}`;
    this.form.get('code')?.disable();
    this.form.get('code')?.setValue(this.item.code);
    this.form.get('name')?.enable();
    this.form.get('name')?.setValue(this.item.name);
    this.form.get('order')?.enable();
    this.form.get('order')?.setValue(this.item.order);
    this.form.get('status')?.enable();
    this.form.get('status')?.setValue(this.item.status);
    this.form.get('description')?.enable();
    this.form.get('description')?.setValue(this.item.description);
  }
  updateFormToInfo(): void {
    this.isInfo = true;
    this.isEdit = false;
    this.isAdd = false;
    this.tittle = `Chi tiết ${this.moduleName}`;
    this.form.get('code')?.disable();
    this.form.get('code')?.setValue(this.item.code);
    this.form.get('name')?.disable();
    this.form.get('name')?.setValue(this.item.name);
    this.form.get('order')?.disable();
    this.form.get('order')?.setValue(this.item.order);
    this.form.get('status')?.disable();
    this.form.get('status')?.setValue(this.item.status);
    this.form.get('description')?.disable();
    this.form.get('description')?.setValue(this.item.description);
  }
  //#endregion Update-form-type

  public initData(data: any, type: any = null, option: any = {}): void {
    this.resetForm();
    this.isLoading = false;
    this.isReloadGrid = false;
    this.item = data;
    this.type = type;
    this.option = option;
    switch (type) {
      case FORM_TYPE.ADD:
        this.updateFormToAdd();
        break;
      case FORM_TYPE.INFO:
        this.updateFormToInfo();
        break;
      case FORM_TYPE.EDIT:
        this.updateFormToEdit();
        break;
      default:
        this.updateFormToAdd();
        break;
    }
  }

  resetForm(): void {
    this.form.reset();
    this.form.get('status')?.setValue(true);
    this.form.get('order')?.setValue(0);
  }

  closeModalReloadData(): void {
    this.isVisible = false;
    this.eventEmmit.emit({ type: EVENT_TYPE.SUCCESS });
  }

  save(isCreateAfter: boolean = false): Subscription | undefined {
    this.isLoading = true;

    cleanForm(this.form);
    // tslint:disable-next-line:forin
    for (const i in this.form.controls) {
      this.form.controls[i].markAsDirty();
      this.form.controls[i].updateValueAndValidity();
    }
    if (!this.form.valid) {
      this.isLoading = false;
      this.messageService.error(this.i18n.fanyi('common.form.invalid-data'));
      return;
    }

    const data = {
      id: this.item.id,
      code: this.form.get('code')?.value,
      name: this.form.get('name')?.value,
      order: this.form.get('order')?.value,
      status: this.form.get('status')?.value,
      description: this.form.get('description')?.value
    };
    if (data.code === null || data.code === undefined || data.code === '') {
      this.isLoading = false;
      this.messageService.error(`Mã ứng dụng không được để trống!`);
      return;
    }
    if (data.name === null || data.name === undefined || data.name === '') {
      this.isLoading = false;
      this.messageService.error(`Tên ứng dụng không được để trống!`);
      return;
    }

    if (this.isAdd) {
      const promise = this.applicationApiService.create(data).subscribe({
        next: (res: any) => {
          if (res.data === null || res.data === undefined) {
            this.messageService.error(`${res.message}`);
            return;
          }
          this.messageService.success(`${res.message}`);
          this.isReloadGrid = true;
          if (isCreateAfter) {
            this.resetForm();
          } else {
            this.closeModalReloadData();
          }
        },
        error: (err: any) => {
          if (err.error) {
            this.messageService.error(`${err.error.message}`);
          } else {
            this.messageService.error(`${err.status}`);
          }
        },
        complete: () => (this.isLoading = false)
      });
      return promise;
    } else if (this.isEdit) {
      const promise = this.applicationApiService.update(data).subscribe({
        next: (res: any) => {
          if (res.data === null || res.data === undefined) {
            this.messageService.error(`${res.message}`);
            return;
          }
          this.messageService.success(`${res.message}`);
          this.closeModalReloadData();
        },
        error: (err: any) => {
          if (err.error) {
            this.messageService.error(`${err.error.message}`);
          } else {
            this.messageService.error(`${err.status}`);
          }
        },
        complete: () => (this.isLoading = false)
      });
      return promise;
    } else {
      return;
    }
  }
}
