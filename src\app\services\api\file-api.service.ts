import { Injectable } from '@angular/core';
import { _HttpClient } from '@delon/theme';
import { environment } from '@env/environment';
import { fileRouter } from '@util';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class FileApiService {
  constructor(private http: _HttpClient) {}
  uploadObject(model: any, formData: FormData): Observable<any> {
    return this.http.post(environment.api.baseUrl + fileRouter.uploadObject, formData, {});
  }
  downloadFileBase64(id: any): Observable<any> {
    return this.http.get(environment.api.baseUrl + fileRouter.downloadFile + id);
  }
}
