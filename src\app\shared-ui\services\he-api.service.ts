import { Injectable } from '@angular/core';
import { _HttpClient } from '@delon/theme';
import { environment } from '@env/environment';
// RxJS
import { Observable } from 'rxjs';

import { heRouter } from '../utils/shared-api-router';

@Injectable({
  providedIn: 'root'
})
export class HeApiService {
  constructor(private http: _HttpClient) {}
  getCombobox(): Observable<any> {
    return this.http.get(environment.api.baseUrl + heRouter.getCombobox);
  }
}
