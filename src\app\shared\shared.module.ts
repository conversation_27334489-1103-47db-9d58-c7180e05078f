/* eslint-disable import/order */
import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { DelonACLModule } from '@delon/acl';
import { DelonFormModule } from '@delon/form';
import { AlainThemeModule } from '@delon/theme';

import { SHARED_DELON_MODULES } from './shared-delon.module';
import { SHARED_ZORRO_MODULES } from './shared-zorro.module';

// #region third libs
import { DragDropModule } from '@angular/cdk/drag-drop';
const THIRDMODULES = [DragDropModule];
// #endregion

// #region your componets & directives
import { PRO_SHARED_MODULES } from '../layout/pro';
import { AddressModule } from './components/address';
import { DelayModule } from './components/delay';
import { EditorModule } from './components/editor';
import { FileManagerModule } from './components/file-manager';
import { MasonryModule } from './components/masonry';
import { MouseFocusModule } from './components/mouse-focus';
import { ScrollbarModule } from './components/scrollbar';
import { StatusLabelModule } from './components/status-label';

import { AggridCellRenderModule } from './ag-grid';
import { PaginationModule } from './components/pagination';

import { NzSkeletonModule } from 'ng-zorro-antd/skeleton';
import { HeKhoaLopComponent } from '../shared-ui/components/he-khoa-lop/he-khoa-lop.component';
import { HeKhoaLopTreeviewComponent } from '../shared-ui/components/he-khoa-lop-treeview/he-khoa-lop-treeview.component';
import { SelectClassDynamicComponent } from '../shared-ui/components/select-class-dynamic/select-class-dynamic.component';
import { AgGridModule } from 'ag-grid-angular';

const MODULES = [
  AddressModule,
  DelayModule,
  EditorModule,
  FileManagerModule,
  MasonryModule,
  MouseFocusModule,
  ScrollbarModule,
  StatusLabelModule,
  AggridCellRenderModule,
  PaginationModule,
  NzSkeletonModule,
  ...PRO_SHARED_MODULES
];
const COMPONENTS = [HeKhoaLopComponent, HeKhoaLopTreeviewComponent, SelectClassDynamicComponent];

// #endregion

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    RouterModule,
    ReactiveFormsModule,
    AlainThemeModule.forChild(),
    DelonACLModule,
    DelonFormModule,
    ...SHARED_DELON_MODULES,
    ...SHARED_ZORRO_MODULES,
    ...MODULES,
    // third libs
    ...THIRDMODULES,
    AgGridModule
  ],
  declarations: COMPONENTS,
  exports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    RouterModule,
    AlainThemeModule,
    DelonACLModule,
    DelonFormModule,
    // i18n
    ...SHARED_DELON_MODULES,
    ...SHARED_ZORRO_MODULES,
    ...MODULES,
    // third libs
    ...THIRDMODULES,
    ...COMPONENTS
  ]
})
export class SharedModule {}
