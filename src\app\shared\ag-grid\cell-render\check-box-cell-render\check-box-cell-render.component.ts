import { Component } from '@angular/core';
import { ICellRendererAngularComp } from 'ag-grid-angular';
import { IAfterGuiAttachedParams } from 'ag-grid-community';

@Component({
  selector: 'app-check-box-cell-render',
  templateUrl: './check-box-cell-render.component.html',
  styleUrls: []
})
export class CheckBoxCellRenderComponent implements ICellRendererAngularComp {
  constructor() {}
  isBoolean: boolean = false;

  params: any;

  refresh(params: any): boolean {
    return false;
  }

  onCheckboxChange(event: any): void {
    const newValue = event.target.checked ? 1 : 0; // Giá trị mới (1 cho checked, 0 cho unchecked)
    this.params.node.setDataValue(this.params.colDef.field, newValue); // Cập nhật giá trị vào grid
  }

  agInit(params: any): void {
    this.params = params;
  }
}
