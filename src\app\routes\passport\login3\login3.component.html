<pro-langs class="pro-passport__langs"></pro-langs>
<div class="pro-passport__bg width-100" style="background-image: url('./assets/tmp/img-big/bg-2.jpeg')">
  <div class="pro-passport__bg-overlay"></div>
  <div class="pro-passport__form">
    <div class="pro-passport__form-logo"><img src="./assets/logo-thienan.png" /></div>
    <h3 class="pro-passport__form-title">{{ 'app.register.register' | i18n }}</h3>
    <form nz-form #f="ngForm" nzLayout="vertical" [formGroup]="form" (ngSubmit)="submit()" role="form" se-container>
      <nz-alert *ngIf="error" [nzType]="'error'" [nzMessage]="error" [nzShowIcon]="true" class="mb-lg"></nz-alert>
      <se [label]="'validation.phone-number' | i18n" required [error]="'validation.phone-number.wrong-format' | i18n">
        <nz-input-group nzSize="large" [nzAddOnBefore]="addOnBeforeTemplate">
          <ng-template #addOnBeforeTemplate>
            <nz-select formControlName="mobilePrefix" style="width: 80px">
              <nz-option [nzLabel]="'+86'" [nzValue]="'+86'"></nz-option>
              <nz-option [nzLabel]="'+87'" [nzValue]="'+87'"></nz-option>
            </nz-select>
          </ng-template>
          <input formControlName="mobile" nz-input type="tel" placeholder="Phone number" />
        </nz-input-group>
      </se>
      <se [label]="'validation.oidc-callback-code' | i18n" required [error]="'validation.oidc-callback-code.required' | i18n">
        <nz-row [nzGutter]="8">
          <nz-col [nzSpan]="12">
            <nz-input-group nzSize="large" nzAddonBeforeIcon="anticon anticon-mail">
              <input nz-input formControlName="captcha" type="tel" placeholder="Captcha" />
            </nz-input-group>
          </nz-col>
          <nz-col [nzSpan]="12">
            <button type="button" nz-button nzSize="large" (click)="getCaptcha()" [disabled]="count > 0" nzBlock [nzLoading]="http.loading">
              {{ count ? count + 's' : ('app.register.get-oidc-callback-code' | i18n) }}
            </button>
          </nz-col>
        </nz-row>
      </se>
      <se [label]="'validation.password' | i18n" required [error]="'validation.password.required' | i18n">
        <input nz-input type="password" nzSize="large" formControlName="password" placeholder="Password" />
      </se>
      <se>
        <div class="flex-center-between">
          <button nz-button nzType="primary" nzSize="large" type="submit" [disabled]="f.invalid" [nzLoading]="http.loading">
            {{ 'app.register.register' | i18n }}
          </button>
          <a routerLink="/passport/login">
            {{ 'app.register.sign-in' | i18n }}
          </a>
        </div>
      </se>
    </form>
  </div>
</div>
