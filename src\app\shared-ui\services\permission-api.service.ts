import { Injectable } from '@angular/core';
import { _HttpClient } from '@delon/theme';
import { environment } from '@env/environment';
// RxJS
import { BehaviorSubject, Observable } from 'rxjs';

import { permissionRouter } from '../utils/shared-api-router';

@Injectable({
  providedIn: 'root'
})
export class PermissionApiService {
  constructor(private readonly http: _HttpClient) {}

  getListCombobox(): Observable<any> {
    return this.http.get(environment.api.baseUrl + permissionRouter.getListCombobox);
  }
  private currentPermission = new BehaviorSubject<any>({});
  userPermission = this.currentPermission.asObservable();

  updatePermission(permission: any) {
    this.currentPermission.next(permission);
  }
}
