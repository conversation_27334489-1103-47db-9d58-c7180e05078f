# Uni Diploma UI

## Hướng dẫn build docker image

### Đăng nhập

```bash
docker login -u username -p password docker.io
```

### Build và push diploma fe

```bash
# Build và đóng image
docker build -t thienanunisoft/diploma-fe:latest . -f Dockerfile

# Đóng image dựa vào source đã build trong thư mục dist (cái này sẽ chạy nhanh hơn vì không cần build)
npm run build-origin
docker build -t thienanunisoft/diploma-fe:latest . -f Dockerfile.builded

# Push image lên docker hub
docker push thienanunisoft/diploma-fe:latest
```

## Khởi chạy ứng dụng

- Pull thư viện về máy `yarn install` (trường hợp máy chưa cài yarn `npm i -g yarn`)
- Chạy ứng dụng môi trường dev `npm start` (ứng dụng sẽ chạy ở port 8100)
- Build ứng dụng `npm run build`

## Đa ngôn ngữ

- <PERSON><PERSON><PERSON><PERSON> cấu hình trong file vi-VN.json (cho tiếng Việt) và en-US.json (cho tiếng anh)
- Cấu trúc khi đặt tên đa ngôn ngữ: `<module>.<component>.<tên đối tượng>.<thuộc tính>`

## Submodule

- link submodule: https://gitlab.unisoft.edu.vn/thien-an-group/uni-share-ui.git
- 

- Hướng dẫn sử dụng git submodule tại đây 
```code
https://git-scm.com/book/en/v2/Git-Tools-Submodules
https://topdev.vn/blog/git-submodules-va-ung-dung-trong-viec-chia-se-tai-nguyen-dung-chung
```

- Sử dụng git submodule để quản lý các module con 
```bash
git submodule add https://gitlab.unisoft.edu.vn/thien-an-group/uni-share-ui.git src/app/shared-ui
```

- Khi mà bạn git clone project của bạn trên 1 máy khác, thì nó sẽ không tự động clone các submodule đã add xuống theo mà bạn cần phải chạy update và pull các submodule về.
```bash
git submodule update --init
git submodule update --recursive --remote
git pull --recurse-submodules
```

- Khi submodule đó không sử dụng, hoặc bị lỗi cần add lại thì ta phải remove như thế nào? Chạy các lệnh sau đây theo trình tự để remove submodule
```bash
git submodule deinit /src/app/shared-ui -f
git rm /src/app/shared-ui
git commit -m "Remove submodule" .
rm -rf .git/modules/src/app/shared-ui
```


## Hướng dẫn cài đặt và cấu hình trang quản trị - Admin
- Môi trường: IIS Windows Server

## Hướng dẫn cài đặt
- Cấu hình site trên IIS Windows Server và giải nén thư mục code đã build của trang admin
- Copy file web.config vào thư mục đã build (mục đích để chạy được routing của angular, redirect về trang phù hợp)
- Chỉnh sửa tham số file env.json trong thư mục assets theo cấu hình phù hợp với trang admin
