<div class="background">
  <form nz-form [formGroup]="form" class="form-login" (ngSubmit)="submit()" role="form">
    <div nz-row class="title-login text-center">
      <b
        ><span id="title-text-two">{{ 'form.login' | i18n }}</span></b
      >
    </div>
    <nz-alert *ngIf="error" [nzType]="'error'" [nzMessage]="error" [nzShowIcon]="true" class="mb-lg"></nz-alert>
    <nz-form-item>
      <nz-form-control [nzSpan]="24" [nzErrorTip]="'identity card number.required' | i18n">
        <nz-input-group class="form-input-login" nzSize="large" nzPrefixIcon="idcard">
          <input nz-input formControlName="userName" placeholder="{{ 'identity card number.placeholder' | i18n }}" />
        </nz-input-group>
      </nz-form-control>
    </nz-form-item>
    <nz-form-item>
      <nz-form-control [nzSpan]="24" [nzErrorTip]="'password-login.required' | i18n">
        <nz-input-group class="form-input-password" nzSize="large" nzPrefixIcon="lock" [nzSuffix]="suffixIconShowHide">
          <input nz-input [type]="typePassword" formControlName="password" placeholder="{{ 'password.placeholder' | i18n }}" />
        </nz-input-group>
        <ng-template #suffixIconShowHide>
          <span (click)="switchShowPass()" *ngIf="!showPassword"><i nz-icon nzType="eye" nzTheme="outline"></i></span>
          <span (click)="switchShowPass()" *ngIf="showPassword"><i nz-icon nzType="eye-invisible" nzTheme="outline"></i></span>
        </ng-template>
      </nz-form-control>
    </nz-form-item>
    <nz-form-item>
      <nz-form-control [nzSpan]="24">
        <label nz-checkbox class="check-box-text" formControlName="remember">{{ 'app.login.remember-me' | i18n }}</label>
      </nz-form-control>
    </nz-form-item>
    <div nz-row class="text-center">
      <button nz-button type="submit" nzType="primary" nzSize="large" [nzLoading]="isLoading" nzBlock class="btn-login">
        {{ 'app.login.login' | i18n }}
      </button>
      <!-- <nz-col [nzSpan]="15" class="text-right">
        <a class="forgot" routerLink="/passport/forgot-password">{{ 'app.login.forgot-password' | i18n }}</a>
      </nz-col>
      <div class="other">
        {{ 'app.login.sign-in-with' | i18n }}
        <a class="register" routerLink="/passport/register">{{ 'app.login.signup' | i18n }}</a>
      </div> -->
    </div>
  </form>
</div>
