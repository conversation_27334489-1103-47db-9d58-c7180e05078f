.brand {
  // 让 `nz-row` & `nz-col` 包含有边框效果
  &-bordered {
    overflow: hidden;
    border: 1px solid @brand-bordered-color;

    > [class*='ant-col-']::before,
    > [class*='ant-col-']::after {
      position: absolute;
      display: block;
      content: '';
    }

    > [class*='ant-col-']::before {
      right: 0;
      bottom: -1px;
      left: 0;
      height: 0;
      border-top: 1px solid @brand-bordered-color;
    }

    > [class*='ant-col-']::after {
      top: 0;
      bottom: 0;
      left: -1px;
      width: 0;
      border-left: 1px solid @brand-bordered-color;
    }
  }
  // 边框大小为 `2px`
  &-border-width-2 {
    border-width: 2px !important;
  }
}
