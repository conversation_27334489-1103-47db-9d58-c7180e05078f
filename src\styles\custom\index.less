.cs-pagination {
  text-align: right;
}

nz-date-picker{
  width: 100%;
}

nz-select {
  width: 100%;
}

.margin-0 {
  margin: 0;
}

.pull-right {
  text-align: right;
}

.padding-bottom-10 {
  padding-bottom: 10px;
}

.padding-left-10 {
  padding-left: 10px;
}

.ag-watermark {
  display: none !important;
}

.ant-menu-inline.ant-menu-root .ant-menu-item > *, .ant-menu-inline.ant-menu-root .ant-menu-submenu-title > * {
  width: 100%;
}

.margin-bottom-10 {
  margin-bottom: 10px;
}

.custom-footer{
  margin: 0;
  padding-top: 10px;
  padding-bottom:20px ;
}

.ag-header-cell-text
{
	padding: 3px;
}

.so-luong-do {
  color: red !important;
}

.align-center-row {
  display: flex;
  align-items: center;
  height: 100%; 
}
.ag-header{
  background-color: rgb(238, 237, 237) !important;
}
// @card-padding-base: 12px;
