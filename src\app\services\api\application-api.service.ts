import { HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { _HttpClient } from '@delon/theme';
import { environment } from '@env/environment';
import { QueryFilerModel } from '@model';
import { applicationRouter } from '@util';
// RxJS
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class ApplicationApiService {
  constructor(private http: _HttpClient) {}

  create(model: any): Observable<any> {
    return this.http.post(environment.api.baseUrl + applicationRouter.create, model);
  }

  createMany(model: any[]): Observable<any> {
    return this.http.post(environment.api.baseUrl + applicationRouter.createMany, model);
  }

  update(model: any): Observable<any> {
    return this.http.put(environment.api.baseUrl + applicationRouter.update, model);
  }

  getById(id: string): Observable<any> {
    return this.http.get(environment.api.baseUrl + applicationRouter.getById + id);
  }

  delete(list: [string]): Observable<any> {
    const option = {
      headers: new HttpHeaders({
        'Content-Type': 'application/json'
      }),
      body: list
    };
    return this.http.request('delete', environment.api.baseUrl + applicationRouter.delete, option);
  }

  getFilter(model: QueryFilerModel): Observable<any> {
    return this.http.post(environment.api.baseUrl + applicationRouter.getFilter, model);
  }

  getAll(): Observable<any> {
    return this.http.get(environment.api.baseUrl + applicationRouter.getAll);
  }

  getCombobox(): Observable<any> {
    return this.http.get(environment.api.baseUrl + applicationRouter.getCombobox);
  }
}
