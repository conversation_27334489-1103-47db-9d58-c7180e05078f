<result type="success" [title]="title" description="{{ 'app.register-result.activation-email' | i18n }}">
  <ng-template #title>
    <div class="title" style="font-size: 20px">
      {{ 'app.register-result.msg' | i18n : params }}
    </div>
  </ng-template>
  <button (click)="msg.success('email')" nz-button nzSize="large" [nzType]="'primary'">
    {{ 'app.register-result.view-mailbox' | i18n }}
  </button>
  <button routerLink="/" nz-button nzSize="large">
    {{ 'app.register-result.back-home' | i18n }}
  </button>
</result>
