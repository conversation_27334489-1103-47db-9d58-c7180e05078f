import { Component, ElementRef, EventEmitter, Inject, Input, OnInit, Output } from '@angular/core';
import { I18NService } from '@core';
import { ALAIN_I18N_TOKEN } from '@delon/theme';
import { ButtonModel, ExcelColumnMapping } from '@model';
import { ApplicationApiService } from '@service';
import { StatusCellRenderComponent } from '@shared';
import {
  AG_GIRD_CELL_STYLE,
  EVENT_TYPE,
  EXCEL_STYLES_DEFAULT,
  OVERLAY_LOADING_TEMPLATE,
  OVERLAY_NOROW_TEMPLATE,
  stringToBoolean
} from '@util';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzUploadFile } from 'ng-zorro-antd/upload';
import { Observable, Observer, Subscription } from 'rxjs';
import * as XLSX from 'xlsx';

@Component({
  selector: 'app-application-import-item',
  templateUrl: './application-import-item.component.html',
  styleUrls: ['./application-import-item.component.less']
})
export class ApplicationImportItemComponent {
  constructor(
    private messageService: NzMessageService,
    private applicationService: ApplicationApiService,
    @Inject(ALAIN_I18N_TOKEN) private i18n: I18NService,
    private elementRef: ElementRef
  ) {
    //#region Button
    this.btnDownload = {
      title: 'Tải về mẫu',
      visible: true,
      enable: true,
      grandAccess: true,
      click: ($event: any) => {
        this.onExportExcel();
      }
    };
    this.btnUpload = {
      title: 'Tải lên dữ liệu',
      visible: true,
      enable: true,
      grandAccess: true,
      click: ($event: any) => {}
    };
    this.btnReset = {
      title: this.i18n.fanyi('app.common.button.reset'),
      visible: true,
      enable: true,
      grandAccess: true,
      click: ($event: any) => {
        this.onReset();
      }
    };
    this.btnSave = {
      title: this.i18n.fanyi('app.common.button.save'),
      visible: true,
      enable: true,
      grandAccess: true,
      click: ($event: any) => {
        this.save();
      }
    };
    this.btnCancel = {
      title: this.i18n.fanyi('app.common.button.close'),
      visible: true,
      enable: true,
      grandAccess: true,
      click: ($event: any) => {
        this.handleCancel();
      }
    };
    this.btnClose = {
      title: this.i18n.fanyi('app.common.button.close'),
      visible: true,
      enable: true,
      grandAccess: true,
      click: ($event: any) => {
        this.handleCancel();
      }
    };
    //#endregion Button

    //#region Ag-grid
    this.columnDefs = [
      { field: 'index', headerName: this.i18n.fanyi('app.common.table.grid-index'), width: 110 },
      { field: 'code', headerName: 'Mã', editable: true, sortable: true, filter: true, minWidth: 180, flex: 1 },
      { field: 'name', headerName: 'Tên', editable: true, sortable: true, filter: true, minWidth: 180, flex: 1 },
      { field: 'status', headerName: 'Trạng thái', editable: true, cellRenderer: 'statusNameCellRender', minWidth: 150 },
      { field: 'order', headerName: 'Thứ tự sắp xếp', editable: true, sortable: true }
    ];
    this.defaultColDef = {
      minWidth: 100,
      cellStyle: AG_GIRD_CELL_STYLE,
      resizable: true
    };
    this.frameworkComponents = {
      statusCellRender: StatusCellRenderComponent,
      statusNameCellRender: StatusCellRenderComponent
    };
    this.excelStyles = [...EXCEL_STYLES_DEFAULT];
    //#endregion Ag-grid
  }
  @Input() isVisible = false;
  @Output() readonly eventEmmit = new EventEmitter<any>();

  option: any;

  tittle = 'Nhập dữ liệu ứng dụng từ excel';
  moduleName = 'ứng dụng';

  isLoading = false;

  gridApi: any;
  gridColumnApi: any;
  rowData: any[] = [];
  columnDefs: any;
  defaultColDef: any;
  excelStyles: any;
  frameworkComponents: any;
  overlayLoadingTemplate = OVERLAY_LOADING_TEMPLATE;
  overlayNoRowsTemplate = OVERLAY_NOROW_TEMPLATE;

  btnDownload: ButtonModel;
  btnUpload: ButtonModel;
  btnReset: ButtonModel;
  btnSave: ButtonModel;
  btnCancel: ButtonModel;
  btnClose: ButtonModel;

  isCompleteImport = false;
  jsonObject: any[] = [];
  excelColumnsMapping: ExcelColumnMapping[] = [
    {
      gridName: 'code',
      excelName: 'Mã',
      dataType: 'string'
    },
    {
      gridName: 'name',
      excelName: 'Tên',
      dataType: 'string'
    },
    {
      gridName: 'status',
      excelName: 'Trạng thái',
      dataType: 'boolean'
    },
    {
      gridName: 'order',
      excelName: 'Thứ tự sắp xếp',
      dataType: 'number'
    }
  ];

  onExportExcel(): void {
    const params = {
      columnWidth: 100,
      sheetName: this.moduleName,
      exportMode: undefined, // 'xml', // : undefined,
      suppressTextAsCDATA: true,
      rowHeight: undefined,
      fileName: `Biểu mẫu nhập liệu ${this.moduleName}.xlsx`,
      headerRowHeight: undefined, // undefined,
      customHeader: [],
      customFooter: []
    };
    this.gridApi.exportDataAsExcel(params);
  }

  onReset(): void {
    this.gridApi.setRowData([]);
  }

  //#region convertExcelData

  transformFile = (file: NzUploadFile) => {
    this.parseExcel(file);
    return new Observable((observer: Observer<Blob>) => {});
    // tslint:disable-next-line: semicolon
  };

  parseExcel(file: any): void {
    const reader = new FileReader();
    reader.onload = (e: any) => {
      const data = e.target.result;
      const workbook = XLSX.read(data, {
        type: 'binary'
      });
      workbook.SheetNames.forEach(sheetName => {
        // Here is your object
        const XL_row_object = XLSX.utils.sheet_to_json(workbook.Sheets[sheetName]);
        this.jsonObject = [...XL_row_object];
      });

      const listObj: any[] = [];
      let i = 0;
      this.jsonObject.forEach(itemExcel => {
        const itemGrid: any = { index: ++i };
        this.excelColumnsMapping.forEach(col => {
          this.convertExcelToGrid(itemGrid, itemExcel);
        });
        listObj.push(itemGrid);
      });

      this.gridApi.setRowData(listObj);
      if (listObj.length === 0) {
        this.messageService.error(`Dữ liệu tải lên không phù hợp`);
      } else {
        this.messageService.success(`Tải lên dữ liệu thành công`);
      }
    };

    reader.onerror = ex => {
      this.messageService.error(`Tải lên dữ liệu thất bại - ${ex}`);
    };

    reader.readAsBinaryString(file);
  }

  convertExcelToGrid = (itemGrid: any, itemExcel: any): void => {
    this.excelColumnsMapping.forEach(col => {
      if (col.dataType === 'string') {
        itemGrid[col.gridName!] = `${itemExcel[col.excelName!]}`;
      } else if (col.dataType === 'number') {
        itemGrid[col.gridName!] = parseFloat(itemExcel[col.excelName!]);
      } else if (col.dataType === 'boolean') {
        itemGrid[col.gridName!] = stringToBoolean(itemExcel[col.excelName!]);
      } else {
        itemGrid[col.gridName!] = itemExcel[col.excelName!];
      }
    });
    // tslint:disable-next-line:semicolon
  };

  ////#endregion convertExcelData

  //#region Ag-grid
  onGridReady(params: any): void {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    this.gridApi.hideOverlay();
  }
  //#endregion Ag-grid

  handleCancel(): void {
    this.isVisible = false;
    if (this.isCompleteImport) {
      this.eventEmmit.emit({ type: EVENT_TYPE.SUCCESS });
    } else {
      this.eventEmmit.emit({ type: EVENT_TYPE.CLOSE });
    }
  }

  public initData(option: any = {}): void {
    this.option = option;
    this.isCompleteImport = false;
    this.isVisible = true;
  }

  closeModalReloadData(): void {
    this.isVisible = false;
    this.eventEmmit.emit({ type: EVENT_TYPE.SUCCESS });
  }

  //#region API Event

  save(): Subscription {
    this.isLoading = true;
    const data: any[] = [];
    // iterate through every node in the grid
    this.gridApi.forEachNode((rowNode: any, index: number) => {
      data.push(rowNode.data);
    });

    if (data.length === 0) {
      this.isLoading = false;
      this.messageService.error(`Danh sách ${this.moduleName} không có dữ liệu!`);
      return new Subscription();
    }

    const promise = this.applicationService.createMany(data).subscribe({
      next: (res: any) => {
        if (res.data === null || res.data === undefined) {
          this.messageService.error(`${res.message}`);
          return;
        }
        this.messageService.success(`${res.message}`);
        this.isCompleteImport = true;
      },
      error: (err: any) => {
        if (err.error) {
          this.messageService.error(`${err.error.message}`);
        } else {
          this.messageService.error(`${err.status}`);
        }
      },
      complete: () => (this.isLoading = false)
    });
    return promise;
  }
  //#endregion API Event
}
