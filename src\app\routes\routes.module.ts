// dashboard pages

import { NgModule, Type } from '@angular/core';
import { SharedModule } from '@shared';
import { AgChartsAngularModule } from 'ag-charts-angular';
import { AgGridModule } from 'ag-grid-angular';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { NzProgressModule } from 'ng-zorro-antd/progress';
import { NzTimelineModule } from 'ng-zorro-antd/timeline';

import { SharedLogoutComponent } from '../shared-ui/routes/logout/logout.component';
import { SharedOIDCCallBackComponent } from '../shared-ui/routes/oidc-callback/oidc-callback.component';
import { AgGridComponent } from './ag-grid/demo/ag-grid.component';
import { DashboardComponent } from './dashboard/dashboard.component';
import { RouteRoutingModule } from './routes-routing.module';

const COMPONENTS: Array<Type<void>> = [DashboardComponent, SharedOIDCCallBackComponent, SharedLogoutComponent, AgGridComponent];
const COMPONENTS_NOROUNT: Array<Type<void>> = [];

@NgModule({
  imports: [SharedModule, RouteRoutingModule, AgGridModule, NzCheckboxModule, NzTimelineModule, NzProgressModule, AgChartsAngularModule],
  declarations: [...COMPONENTS, ...COMPONENTS_NOROUNT],
  entryComponents: COMPONENTS_NOROUNT
})
export class RoutesModule {}
