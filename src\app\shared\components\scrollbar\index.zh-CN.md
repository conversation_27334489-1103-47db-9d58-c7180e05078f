---
order: 40
title: scrollbar
type: Component
---

基于 [perfect-scrollbar](http://utatti.github.io/perfect-scrollbar/) 自定义滚动条插件，参考[示例](https://preview.ng-alain.com/pro/#/other/chat)。

## API

| 参数   | 说明    | 类型  | 默认值 |
| ----- | ------ | ----- | ------ |
| `[options]` | [选项](https://github.com/utatti/perfect-scrollbar#options) | `ScrollbarOptions` | -  |
| `[disabled]` | 是否禁用 | `boolean` | `false` |
| `[psScrollX]` | `ps-scroll-x` 事件 | `EventEmitter<any>` | - |
| `[psScrollY]` | `ps-scroll-y` 事件 | `EventEmitter<any>` | - |
| `[psScrollUp]` | `ps-scroll-up` 事件 | `EventEmitter<any>` | - |
| `[psScrollDown]` | `ps-scroll-down` 事件 | `EventEmitter<any>` | - |
| `[psScrollLeft]` | `ps-scroll-left` 事件 | `EventEmitter<any>` | - |
| `[psScrollRight]` | `ps-scroll-right` 事件 | `EventEmitter<any>` | - |
| `[psXReachStart]` | `ps-x-reach-start` 事件 | `EventEmitter<any>` | - |
| `[psXReachEnd]` | `ps-x-reach-end` 事件 | `EventEmitter<any>` | - |
| `[psYReachStart]` | `ps-y-reach-start` 事件 | `EventEmitter<any>` | - |
| `[psYReachEnd]` | `ps-y-reach-end` 事件 | `EventEmitter<any>` | - |
