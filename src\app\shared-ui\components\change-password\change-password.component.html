<nz-modal
  [(nzVisible)]="isVisible"
  [nzTitle]="modalTitle"
  [nzContent]="modalContent"
  [nzFooter]="modalFooter"
  nzMaskClosable="false"
  nzWidth="600px"
  (nzOnCancel)="handleCancel()"
>
  <ng-template #modalTitle> {{ tittle }} </ng-template>

  <ng-template #modalContent>
    <form nz-form [formGroup]="form" class="form-login" (ngSubmit)="submit()" role="form">
      <nz-form-item *ngIf="success || error">
        <nz-form-control *ngIf="success || error" [nzSpan]="24" [nzErrorTip]="'password-login.required' | i18n">
          <nz-alert *ngIf="success" [nzType]="'success'" [nzMessage]="success" [nzShowIcon]="true" class="mb-lg"></nz-alert>
          <nz-alert *ngIf="error" [nzType]="'error'" [nzMessage]="error" [nzShowIcon]="true" class="mb-lg"></nz-alert>
        </nz-form-control>
      </nz-form-item>
      <nz-form-item *ngIf="!success">
        <nz-form-control [nzSpan]="24" [nzErrorTip]="'password-change.required' | i18n">
          <nz-input-group class="form-input-password" nzPrefixIcon="lock" [nzSuffix]="suffixIconShowHide">
            <input nz-input [type]="typeOldPassword" formControlName="oldPassword" placeholder="{{ 'old-password.placeholder' | i18n }}" />
          </nz-input-group>
          <ng-template #suffixIconShowHide>
            <span (click)="switchShowOldPass()" *ngIf="!showOldPassword"><i nz-icon nzType="eye" nzTheme="outline"></i></span>
            <span (click)="switchShowOldPass()" *ngIf="showOldPassword"><i nz-icon nzType="eye-invisible" nzTheme="outline"></i></span>
          </ng-template>
        </nz-form-control>
      </nz-form-item>
      <nz-form-item *ngIf="!success">
        <nz-form-control [nzSpan]="24" [nzErrorTip]="errorNewPassword">
          <nz-input-group class="form-input-password" nzPrefixIcon="lock" [nzSuffix]="suffixIconShowHide">
            <input nz-input [type]="typePassword" formControlName="newPassword" placeholder="{{ 'new-password.placeholder' | i18n }}" />
          </nz-input-group>
          <ng-template #suffixIconShowHide>
            <span (click)="switchShowPass()" *ngIf="!showPassword"><i nz-icon nzType="eye" nzTheme="outline"></i></span>
            <span (click)="switchShowPass()" *ngIf="showPassword"><i nz-icon nzType="eye-invisible" nzTheme="outline"></i></span>
          </ng-template>
          <ng-template #errorNewPassword let-control>
            <ng-container *ngIf="control.hasError('required')">{{ 'password-change.required' | i18n }}</ng-container>
            <ng-container *ngIf="control.hasError('pattern')">{{ 'password-change.password-strength' | i18n }}</ng-container>
          </ng-template>
        </nz-form-control>
      </nz-form-item>
      <nz-form-item *ngIf="!success">
        <nz-form-control [nzSpan]="24" [nzErrorTip]="errorconfirmNewPassword">
          <nz-input-group class="form-input-password" nzPrefixIcon="lock" [nzSuffix]="suffixIconShowHide1">
            <input
              nz-input
              [type]="typePassword1"
              formControlName="confirmNewPassword"
              placeholder="{{ 'confirm-new-password.placeholder' | i18n }}"
            />
          </nz-input-group>
          <ng-template #suffixIconShowHide1>
            <span (click)="switchShowPass1()" *ngIf="!showPassword1"><i nz-icon nzType="eye" nzTheme="outline"></i></span>
            <span (click)="switchShowPass1()" *ngIf="showPassword1"><i nz-icon nzType="eye-invisible" nzTheme="outline"></i></span>
          </ng-template>

          <ng-template #errorconfirmNewPassword let-control>
            <ng-container *ngIf="control.hasError('required')">{{ 'password-change-confirm.required' | i18n }}</ng-container>
            <ng-container *ngIf="control.hasError('passwordMismatch')">{{
              'password-change-confirm.password-mismatch' | i18n
            }}</ng-container>
          </ng-template>
        </nz-form-control>
      </nz-form-item>
    </form>
  </ng-template>

  <ng-template #modalFooter>
    <button
      nz-button
      nzType="primary"
      class="btn-primary"
      *ngIf="btnSave.visible && btnSave.grandAccess"
      [nzLoading]="isLoading"
      (click)="btnSave.click($event)"
    >
      <i nz-icon nzType="save" nzTheme="fill"></i>{{ btnSave.title }}
    </button>
    <button
      nz-button
      nzType="default"
      class="btn-warning"
      *ngIf="btnCancel.visible && btnCancel.grandAccess"
      (click)="btnCancel.click($event)"
    >
      <i nz-icon nzType="close-circle" nzTheme="fill"></i>{{ btnCancel.title }}
    </button>
  </ng-template>
</nz-modal>
