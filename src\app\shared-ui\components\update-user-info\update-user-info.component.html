<nz-modal
  [(nzVisible)]="isVisible"
  [nzTitle]="modalTitle"
  [nzContent]="modalContent"
  [nzFooter]="modalFooter"
  nzMaskClosable="false"
  nzWidth="600px"
  (nzOnCancel)="handleCancel()"
>
  <ng-template #modalTitle> {{ tittle }} </ng-template>

  <ng-template #modalContent>
    <form nz-form [formGroup]="form" class="form-login" (ngSubmit)="submit()" role="form">
      <nz-form-item *ngIf="success || error">
        <nz-form-control *ngIf="success || error" [nzSpan]="24">
          <nz-alert *ngIf="success" [nzType]="'success'" [nzMessage]="success" [nzShowIcon]="true" class="mb-lg"></nz-alert>
          <nz-alert *ngIf="error" [nzType]="'error'" [nzMessage]="error" [nzShowIcon]="true" class="mb-lg"></nz-alert>
        </nz-form-control>
      </nz-form-item>
      <nz-form-item *ngIf="!success">
        <nz-form-control [nzSpan]="24" [nzErrorTip]="'update-user-info.full-name.required' | i18n">
          <nz-input-group class="form-full-name">
            <input nz-input type="text" formControlName="fullName" placeholder="{{ 'update-user-info.full-name.placeholder' | i18n }}" />
          </nz-input-group>
        </nz-form-control>
      </nz-form-item>
      <nz-form-item *ngIf="!success">
        <nz-form-control [nzSpan]="24" [nzErrorTip]="'update-user-info.email.invalid-format' | i18n">
          <nz-input-group class="form-email">
            <input nz-input type="text" formControlName="email" placeholder="{{ 'update-user-info.email.placeholder' | i18n }}" />
          </nz-input-group>
        </nz-form-control>
      </nz-form-item>

      <nz-form-item *ngIf="!success">
        <nz-form-control [nzSpan]="24" [nzErrorTip]="'update-user-info.phoneNumber.invalid-format' | i18n">
          <nz-input-group class="form-phoneNumber">
            <input nz-input type="text" formControlName="dienThoai" placeholder="{{ 'update-user-info.phoneNumber.placeholder' | i18n }}" />
          </nz-input-group>
        </nz-form-control>
      </nz-form-item>
    </form>
  </ng-template>

  <ng-template #modalFooter>
    <button
      nz-button
      nzType="primary"
      class="btn-primary"
      *ngIf="btnSave.visible && btnSave.grandAccess && !success"
      [nzLoading]="isLoading"
      (click)="btnSave.click($event)"
    >
      <i nz-icon nzType="save" nzTheme="fill"></i>{{ btnSave.title }}
    </button>
    <button
      nz-button
      nzType="default"
      class="btn-warning"
      *ngIf="btnCancel.visible && btnCancel.grandAccess"
      (click)="btnCancel.click($event)"
    >
      <i nz-icon nzType="close-circle" nzTheme="fill"></i>{{ btnCancel.title }}
    </button>
  </ng-template>
</nz-modal>
