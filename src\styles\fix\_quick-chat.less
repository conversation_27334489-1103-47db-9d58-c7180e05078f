@quick-chat-prefix: ~'.quick-chat';

@{quick-chat-prefix} {
  position: fixed;
  top: auto;
  right: 0;
  bottom: 0;
  left: auto;
  z-index: 98;
  display: flex;
  flex: 1;
  flex-direction: column;
  width: 320px;
  margin-right: 30px;
  line-height: initial;
  background-color: @quick-chat-bg;
  border: 1px solid @quick-chat-border-color;
  border-bottom: none;
  border-radius: 4px 4px 0 0;
  box-shadow: 0 16px 24px 2px rgba(0, 0, 0, 0.14);
  &__bar {
    display: flex;
    flex-direction: row;
    align-items: center;
    height: @quick-chat-bar-height;
    border-bottom: 1px solid @quick-chat-border-color;
    cursor: pointer;
    user-select: none;
    &--title {
      flex: 1;
      height: @quick-chat-bar-height;
      padding-left: 16px;
      font-size: 14px;
      line-height: @quick-chat-bar-height;
      &:hover {
        background-color: lighten(@quick-chat-bg, 2%);
      }
      &-has-message {
        animation: antSwingIn 1500ms infinite;
      }
    }
    &--menu {
      display: flex;
      align-items: center;
      height: @quick-chat-bar-height;
      // padding: 0 8px 0 16px;
      .anticon {
        font-size: 18px;
      }
    }
    &--close {
      padding: 0 16px 0 8px;
    }
  }
  &__body {
    max-height: @quick-chat-content-height + @quick-chat-reply-height;
    transition: max-height 0.5s cubic-bezier(0, 1, 0, 1);
  }
  &__content {
    position: relative;
    display: flex;
    flex-direction: column;
    height: @quick-chat-content-height;
    .chat__scroll-container {
      display: block;
    }
  }
  &__reply {
    height: @quick-chat-reply-height;
    padding: 8px;
    border-top: 1px solid @quick-chat-border-color;
    &--ipt {
      display: block;
      width: 100%;
      height: 100%;
      background: transparent;
      border: none;
      outline: none;
    }
  }

  .chat__message {
    align-items: center;
    &-avatar {
      width: 28px;
    }
  }
  .chat__user-avatar {
    height: 20px;
  }

  // When collapsed
  &__collapsed {
    @{quick-chat-prefix}__body {
      max-height: 0;
      .chat__scroll-container {
        display: none;
      }
    }
  }
}

.quick-chat-rtl-mixin(@enabled) when(@enabled=true) {
  [dir='rtl'] {
    @{quick-chat-prefix} {
      right: inherit;
      left: 0;
      margin-right: inherit;
      margin-left: 30px;
      &__bar {
        &--title {
          padding-right: 16px;
          padding-left: 0;
        }
      }
    }
  }
}
.quick-chat-rtl-mixin(@rtl-enabled);
