@header-search-prefix: ~'@{alain-pro-prefix}__header-search';

@{header-search-prefix} {
  display: flex;
  .anticon-search {
    font-size: 16px;
    cursor: pointer;
  }
  &-input {
    width: 0;
    overflow: hidden;
    background: transparent;
    border-radius: 0;
    transition: width 0.3s, margin-left 0.3s;
    .@{ant-prefix}-select-selection {
      background: transparent;
    }
    input {
      padding-right: 0;
      padding-left: 0;
      background: transparent;
      border: 0;
      outline: none;
      box-shadow: none;
    }
    &,
    &:hover,
    &:focus {
      border-bottom: 1px solid @border-color-base;
    }
  }
  &-show {
    &:hover {
      background: none !important;
    }
    @{header-search-prefix}-input {
      width: @alain-pro-header-search-width;
      margin-left: 8px;
    }
  }
}

@{alain-pro-prefix}__dark {
  @{alain-pro-prefix}__top-nav {
    @{header-search-prefix}-show {
      .@{ant-prefix}-input {
        color: #fff;
      }
    }
  }
}
