import { Component, ChangeDetectionStrategy, OnInit, ChangeDetectorRef, Inject } from '@angular/core';
import { I18NService } from '@core';
import { ALAIN_I18N_TOKEN } from '@delon/theme';
import { SharedAuthService } from '@shared-service';
import { AgChartOptions } from 'ag-charts-community';
import { NzMessageService } from 'ng-zorro-antd/message';
// import { DashboardApiService } from 'src/app/services/api/dashboard-api.service';

@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.less'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class DashboardComponent implements OnInit {
  title = 'CompanyEmployees.Client.Oidc';
  public userAuthenticated = false;
  public isUserAuthenticated: boolean = false;
  public isUserAdmin: boolean = false;

  public isLoading: boolean = false;
  public isLoadingPage: boolean = true;
  public list: Array<{ id: number; title: string; avatar: string; description: string } | null> = [null];
  timelineData: any[] = [];
  options: AgChartOptions = {
    data: [
      {
        asset: 'Số sinh viên đã nộp',
        amount: 65
      },
      {
        asset: 'Số sinh viên chưa nộp',
        amount: 35
      }
    ],
    series: [
      {
        type: 'pie',
        angleKey: 'amount',
        legendItemKey: 'asset',
        sectorLabelKey: 'amount',
        calloutLabel: {
          enabled: true,
          formatter: params => {
            const percentage = Math.round(params.datum.amount);
            return `${params.datum.asset} (${percentage}%)`;
          }
        },
        sectorLabel: {
          color: 'rgba(255, 255, 255, 1)',
          fontWeight: '500',
          formatter: ({ value }) => `${value}%`
        },
        tooltip: {
          enabled: true,
          renderer: params => {
            const percentage = Math.round(params.datum.amount);
            return {
              content: `${params.datum.asset}: ${percentage}%`
            };
          }
        }
      }
    ],
    legend: {
      item: {
        label: {
          fontFamily: 'Roboto, sans-serif',
          fontSize: 14,
          fontWeight: 500,
          color: 'rgba(0, 0, 0, 1)',
          maxLength: 100
        }
      }
    },
    padding: {
      right: 20,
      left: 1
    }
  };

  chartOptions: any = {
    data: [],
    title: {
      text: 'BIỂU ĐỒ BIẾN ĐỘNG SINH VIÊN THEO THÁNG NĂM HỌC 2024-2025',
      fontSize: 18,
      color: '#6cb8ff',
      textAlign: 'left',
      fontFamily: 'Roboto, sans-serif'
    },
    series: [
      {
        type: 'area',
        xKey: 'Thang',
        yKey: 'Dang_hoc',
        stroke: '#5090dc',
        marker: {
          enabled: true,
          fill: '#5090dc'
        },
        yName: 'Số SV đang học',
        group: 'group1'
      },
      {
        type: 'area',
        xKey: 'Thang',
        yKey: 'Nghi_hoc',
        stroke: '#ff9e38',
        marker: {
          enabled: true,
          fill: '#ff9e38'
        },
        yName: 'Số SV nghỉ học',
        group: 'group2'
      }
    ],
    axes: [
      {
        type: 'string',
        position: 'bottom',
        title: {
          text: 'Tháng'
        }
      },
      {
        type: 'number',
        position: 'left',
        title: {
          text: 'Số SV'
        },
        min: 0,
        max: 'auto',
        interval: 2
      }
    ],
    legend: {
      position: 'bottom'
    },
    height: 500,
    options: {
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        y: {
          beginAtZero: true
        }
      }
    }
  };

  chartOptions3: any = {
    data: [
      {
        "Ky": "Kỳ 1",
        "Da_thu": 20454.86,
        "Phai_thu": 21054.80,
        "Da_chi": 15890
      },
      {
        "Ky": "Kỳ 2",
        "Da_thu": 19454.86,
        "Phai_thu": 22054.80,
        "Da_chi": 16890
      },
      {
        "Ky": "Kỳ 3",
        "Da_thu": 15454.86,
        "Phai_thu": 16054.80,
        "Da_chi": 12890
      },
      {
        "Ky": "Kỳ 4",
        "Da_thu": 15454.86,
        "Phai_thu": 19054.80,
        "Da_chi": 12890
      },
    ],
    title: {
      text: 'BIỂU ĐỒ THU CHI NĂM HỌC 2024-2025',
      fontSize: 18,
      color: '#6cb8ff',
      textAlign: 'left',
      fontFamily: 'Roboto, sans-serif'
    },
    series: [
      {
        type: 'bar',
        xKey: 'Ky',
        yKey: 'Phai_thu',
        yName: 'Phải thu',
        group: 'group1'
      },
      {
        type: 'bar',
        xKey: 'Ky',
        yKey: 'Da_thu',
        title: 'Đã thu',
        group: 'group2',
        yName: 'Đã thu'
      },
      {
        type: 'bar',
        xKey: 'Ky',
        yKey: 'Da_chi',
        yName: 'Đã chi'
      }
    ],
    axes: [
      {
        type: 'string',
        position: 'bottom',
        title: {
          text: 'Kỳ học'
        }
      },
      {
        type: 'number',
        position: 'left',
        title: {
          text: 'triệu đồng'
        },
        min: 0,
        max: 'auto',
        interval: 2
      }
    ],
    legend: {
      position: 'bottom'
    },
    height: 500,
    options: {
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        y: {
          beginAtZero: true
        }
      }
    }
  };

  data: any = {};
  teachingData: any = [];

  constructor(
    private _authService: SharedAuthService,
    private changeDetectorRef: ChangeDetectorRef,
    private message: NzMessageService,
    // private dashboardApiService: DashboardApiService,
    @Inject(ALAIN_I18N_TOKEN) private i18n: I18NService
  ) {
    this._authService.loginChanged.subscribe(userAuthenticated => {
      this.userAuthenticated = userAuthenticated;
    });
    this._authService.loginChanged.subscribe(res => {
      this.isUserAuthenticated = res;
      this.isAdmin();
    });
  }

  public login = () => {
    this._authService.login();
  };

  public logout = () => {
    this._authService.logout();
    // this._authService.finishLogout();
  };

  public isAdmin = () => {
    return this._authService.checkIfUserIsAdmin().then(res => {
      this.isUserAdmin = res;
    });
  };

  ngOnInit(): void {
    this._authService.isAuthenticated().then(userAuthenticated => {
      this.userAuthenticated = userAuthenticated;
    });
    this.getClassStatistics();
    this.getSchedule();
    this.getTeachingProcessClass();
    this.getPointSpectrumSubject();
  }

  public openUrl = (item: any) => {
    if (!item.active) {
      this.message.error('Phần mềm chưa được kích hoạt, vui lòng cấu hình và kích hoạt dịch vụ!');
      return;
    }
    if (item.url === null || item.url === undefined || item.url === '') {
      this.message.error('Chưa cấu hình đường dẫn truy cập');
      return;
    }
    window.open(item.url, '_blank');
  };

  getClassStatistics(): void {
    this.isLoading = true;
    // const model = {};
    // this.dashboardApiService.getGeneralStatistics(model).subscribe({
    //   next: (res: any) => {
    //     this.isLoading = false;
    //     this.data = res.data;

    //     this.changeDetectorRef.detectChanges();
    //   },
    //   error: (err: any) => {
    //     this.isLoading = false;
    //     console.log(err);
    //     //this.message.error('Lỗi khi tải dữ liệu');
    //   }
    // });

    this.data = {};
  }
  getSchedule(): void {
    this.isLoading = true;
    const model = {};

    this.timelineData = [
      {
        "idLichDay": 0,
        "kyHieu": "191092705",
        "tenMon": "Hoạch định chiến lược phát triển kinh tế - xã hội",
        "soTinChi": 2,
        "tenLop": "191092705TC.2_LT",
        "thu": 7,
        "caHoc": "0",
        "tuTiet": 1,
        "denTiet": 3,
        "giaoVien": "Lê Thị Dương1",
        "phongHoc": "Từ Sơn nhà E-E302",
        "tuNgay": "2024-08-05T00:00:00",
        "denNgay": "2024-12-15T00:00:00",
        "hocKy": 55,
        "namHoc": "5555",
        "tuGio": "7",
        "denGio": "9h50",
        "idLop": 12943,
        "idCb": 1250,
        "soSv": 0,
        "idPhong": 411,
        "idMon": 1857,
        "loaiLop": "1",
        "tenLoaiLop": "Tín chỉ",
        "loaiTiet": "LT",
        "ngayGiangDay": "2024-11-30T00:00:00",
        "nghiHoc": false,
        "hocBu": false,
        "trangThaiDiemDanh": 0,
        "tenTrangThaiDiemDanh": "Chưa điểm danh",
        "trangThaiGiangDay": 0,
        "tenTrangThaiGiangDay": "Chưa dạy",
        "lockedDiemDanh": false,
        "lockedSuaTiet": false
      },
      {
        "idLichDay": 0,
        "kyHieu": "191092705",
        "tenMon": "Hoạch định chiến lược phát triển kinh tế - xã hội",
        "soTinChi": 2,
        "tenLop": "191092705TC.2_LT",
        "thu": 7,
        "caHoc": "0",
        "tuTiet": 1,
        "denTiet": 3,
        "giaoVien": "Lê Thị Dương1",
        "phongHoc": "Từ Sơn nhà E-E302",
        "tuNgay": "2024-08-05T00:00:00",
        "denNgay": "2024-12-15T00:00:00",
        "hocKy": 55,
        "namHoc": "5555",
        "tuGio": "7",
        "denGio": "9h50",
        "idLop": 12943,
        "idCb": 1250,
        "soSv": 0,
        "idPhong": 411,
        "idMon": 1857,
        "loaiLop": "1",
        "tenLoaiLop": "Tín chỉ",
        "loaiTiet": "LT",
        "ngayGiangDay": "2024-12-07T00:00:00",
        "nghiHoc": false,
        "hocBu": false,
        "trangThaiDiemDanh": 0,
        "tenTrangThaiDiemDanh": "Chưa điểm danh",
        "trangThaiGiangDay": 0,
        "tenTrangThaiGiangDay": "Chưa dạy",
        "lockedDiemDanh": false,
        "lockedSuaTiet": false
      },
      {
        "idLichDay": 0,
        "kyHieu": "191092705",
        "tenMon": "Hoạch định chiến lược phát triển kinh tế - xã hội",
        "soTinChi": 2,
        "tenLop": "191092705TC.2_LT",
        "thu": 7,
        "caHoc": "0",
        "tuTiet": 1,
        "denTiet": 3,
        "giaoVien": "Lê Thị Dương1",
        "phongHoc": "Từ Sơn nhà E-E302",
        "tuNgay": "2024-08-05T00:00:00",
        "denNgay": "2024-12-15T00:00:00",
        "hocKy": 55,
        "namHoc": "5555",
        "tuGio": "7",
        "denGio": "9h50",
        "idLop": 12943,
        "idCb": 1250,
        "soSv": 0,
        "idPhong": 411,
        "idMon": 1857,
        "loaiLop": "1",
        "tenLoaiLop": "Tín chỉ",
        "loaiTiet": "LT",
        "ngayGiangDay": "2024-12-14T00:00:00",
        "nghiHoc": false,
        "hocBu": false,
        "trangThaiDiemDanh": 0,
        "tenTrangThaiDiemDanh": "Chưa điểm danh",
        "trangThaiGiangDay": 0,
        "tenTrangThaiGiangDay": "Chưa dạy",
        "lockedDiemDanh": false,
        "lockedSuaTiet": false
      }
    ];

    this.timelineData.sort((a, b) => {
      return new Date(a.ngayGiangDay).getTime() - new Date(b.ngayGiangDay).getTime();
    });

    this.timelineData = this.timelineData.slice(0, 3);

    this.timelineData = this.timelineData.map(item => {
      item.formattedTuGio = this.convertTimeTo24HourFormat(item.tuGio);
      item.formattedDenGio = this.convertTimeTo24HourFormat(item.denGio);
      return item;
    });

    this.changeDetectorRef.detectChanges();
  }

  getTeachingProcessClass(): void {
    this.isLoading = true;
    const model = {};
    this.formatChartPieData({});
    this.isLoading = false;
    this.teachingData = {
      "tongSoBuoiGiangDay": 98,
      "tongSoBuoiDaGiangDay": 5,
      "phanTamHoanThanh": 5,
      "chitietLop": [
        {
          "tenMon": "Hoạch định chiến lược phát triển kinh tế - xã hội",
          "kyHieu": "191092705TC_lớp_1",
          "tongSoBuoiGiangDay": 11,
          "tongSoBuoiDaGiangDay": 0,
          "phanTamHoanThanh": 65
        },
        {
          "tenMon": "Quản lý an toàn thực phẩm và vệ sinh ăn uống",
          "kyHieu": "191052042_lớp_1",
          "tongSoBuoiGiangDay": 5,
          "tongSoBuoiDaGiangDay": 0,
          "phanTamHoanThanh": 70
        },
        {
          "tenMon": "Hoạch định chiến lược phát triển kinh tế - xã hội",
          "kyHieu": "191092705TC_lớp_2",
          "tongSoBuoiGiangDay": 19,
          "tongSoBuoiDaGiangDay": 3,
          "phanTamHoanThanh": 55
        },
        {
          "tenMon": "Việt Nam học",
          "kyHieu": "191054033_lớp_1",
          "tongSoBuoiGiangDay": 10,
          "tongSoBuoiDaGiangDay": 0,
          "phanTamHoanThanh": 46
        },
        {
          "tenMon": "Ẩm thực và văn hoá ẩm thực Việt Nam",
          "kyHieu": "191052702_lớp_1",
          "tongSoBuoiGiangDay": 9,
          "tongSoBuoiDaGiangDay": 0,
          "phanTamHoanThanh": 25
        },
        {
          "tenMon": "Nghề giám đốc",
          "kyHieu": "191122009_lớp_1",
          "tongSoBuoiGiangDay": 11,
          "tongSoBuoiDaGiangDay": 0,
          "phanTamHoanThanh": 77
        },
        {
          "tenMon": "Tài nguyên du lịch",
          "kyHieu": "191054015_lớp_1",
          "tongSoBuoiGiangDay": 6,
          "tongSoBuoiDaGiangDay": 2,
          "phanTamHoanThanh": 86
        }
      ]
    };

    this.changeDetectorRef.detectChanges();
    // this.dashboardApiService.getTeachingProcessClass(model).subscribe({
    //   next: (res: any) => {
    //     this.formatChartPieData(res.data);
    //     this.isLoading = false;
    //     this.teachingData = res.data;

    //     this.changeDetectorRef.detectChanges();
    //   },
    //   error: (err: any) => {
    //     this.isLoading = false;
    //     //console.log(err);
    //     //this.message.error('Lỗi khi tải dữ liệu');
    //   }
    // });
  }

  formatChartPieData(data: any): void {
    const formattedData = [
      {
        asset: 'Số tiết đã giảng dạy',
        amount: 40
      },
      {
        asset: 'Số tiết chưa giảng dạy',
        amount: 50
      }
    ];

    this.options.data = formattedData;
    this.changeDetectorRef.detectChanges();
  }
  getPointSpectrumSubject(): void {
    this.isLoading = true;
    const model = {};
    this.isLoading = false;
    this.formatChartBarData([
      {
        "Thang": 1,
        "Dang_hoc": 862,
        "Nghi_hoc": 40
      },
      {
        "Thang": 2,
        "Dang_hoc": 900,
        "Nghi_hoc": 20
      },
      {
        "Thang": 3,
        "Dang_hoc": 867,
        "Nghi_hoc": 15
      },
      {
        "Thang": 4,
        "Dang_hoc": 989,
        "Nghi_hoc": 8
      },
      {
        "Thang": 5,
        "Dang_hoc": 980,
        "Nghi_hoc": 3
      },
      {
        "Thang": 6,
        "Dang_hoc": 826,
        "Nghi_hoc": 30
      },
      {
        "Thang": 7,
        "Dang_hoc": 981,
        "Nghi_hoc": 12
      },
      {
        "Thang": 8,
        "Dang_hoc": 920,
        "Nghi_hoc": 6
      },
      {
        "Thang": 9,
        "Dang_hoc": 950,
        "Nghi_hoc": 10
      },
      {
        "Thang": 10,
        "Dang_hoc": 862,
        "Nghi_hoc": 10
      },
      {
        "Thang": 11,
        "Dang_hoc": 995,
        "Nghi_hoc": 14
      },
      {
        "Thang": 12,
        "Dang_hoc": 912,
        "Nghi_hoc": 20
      }
    ]);
    this.isLoadingPage = false;
    this.changeDetectorRef.detectChanges();
  }
  formatChartBarData(data: any[]): void {
    const updatedOptions = {
      ...this.chartOptions,
      data: data.map(item => ({
        Thang: item.Thang,
        Dang_hoc: item.Dang_hoc,
        Nghi_hoc: item.Nghi_hoc
      }))
    };

    this.chartOptions = updatedOptions;
  }
  convertTimeTo24HourFormat(time: any) {
    var splitTime = time.split('h');
    var hour = parseInt(splitTime[0]);
    var minute = 0;
    if (splitTime.length > 1) {
      minute = parseInt(splitTime[1]);
    }
    var hourString = hour < 10 ? `0${hour}` : hour.toString();
    var minuteString = minute < 10 ? `0${minute}` : minute.toString();
    var secondString = '00';

    return `${hourString}:${minuteString}`;
  }
}
