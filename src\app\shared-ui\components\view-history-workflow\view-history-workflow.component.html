<nz-modal
  [(nzVisible)]="isVisible"
  [nzTitle]="modalTitle"
  [nzContent]="modalContent"
  [nzFooter]="modalFooter"
  nzMaskClosable="false"
  nzWidth="1200px"
  (nzOnCancel)="handleCancel()"
>
  <ng-template #modalTitle> {{ tittle }} </ng-template>

  <ng-template #modalContent>
    <nz-spin [nzSpinning]="isLoading">
      <ag-grid-angular
        #agGrid
        style="width: 100%; height: 40vh"
        id="application-grid"
        class="ag-theme-alpine"
        [columnDefs]="columnDefs"
        [defaultColDef]="defaultColDef"
        [suppressRowClickSelection]="true"
        rowSelection="multiple"
        [rowData]="grid.rowData"
        [overlayLoadingTemplate]="overlayLoadingTemplate"
        [overlayNoRowsTemplate]="overlayNoRowsTemplate"
        [components]="frameworkComponents"
        (gridReady)="onGridReady($event)"
      >
      </ag-grid-angular>
    </nz-spin>
  </ng-template>

  <ng-template #modalFooter>
    <button
      nz-button
      nzType="default"
      class="btn-warning"
      *ngIf="btnCancel.visible && btnCancel.grandAccess"
      (click)="btnCancel.click($event)"
    >
      <i nz-icon nzType="close-circle" nzTheme="fill"></i>{{ btnCancel.title }}
    </button>
  </ng-template>
</nz-modal>
