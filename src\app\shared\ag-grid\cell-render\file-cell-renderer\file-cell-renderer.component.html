<button
  *ngIf="params.data.UploadFileGrantAccess && !value; else hasFile"
  style="text-align: center"
  nz-button
  nz-tooltip="Upload"
  (click)="triggerFileUpload()"
>
  <i nz-icon nzType="upload"></i>
  Click Upload
</button>

<input type="file" #fileInput hidden (change)="onFileSelected($event)" />

<ng-template #hasFile>
  <div style="display: flex; align-items: center; justify-content: space-between; width: 100%">
    <div style="display: flex; align-items: center; flex: 1; overflow: hidden">
      <i *ngIf="value" nz-icon nzType="paper-clip" style="margin-right: 8px"></i>
      <span
        (click)="download()"
        [title]="value"
        style="
          color: #1890ff;
          text-decoration: underline;
          cursor: pointer;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          flex: 1;
        "
      >
        {{ value }}
      </span>
    </div>
    <div style="flex-shrink: 0">
      <i
        *ngIf="params.data.dowloadFileGrantAccess && value"
        nz-icon
        nzType="download"
        style="margin-left: 8px; cursor: pointer"
        (click)="download()"
        (mouseenter)="hoveringDownload = true"
        (mouseleave)="hoveringDownload = false"
        [ngStyle]="{ color: hoveringDownload ? '#40a9ff' : 'inherit' }"
      ></i>
      <i
        *ngIf="params.data.deleteFileGrantAccess && value"
        nz-icon
        nzType="delete"
        style="margin-left: 8px; cursor: pointer"
        (click)="delete()"
        (mouseenter)="hoveringDelete = true"
        (mouseleave)="hoveringDelete = false"
        [ngStyle]="{ color: hoveringDelete ? '#ff4d4f' : 'inherit' }"
      ></i>
    </div>
  </div>
</ng-template>
