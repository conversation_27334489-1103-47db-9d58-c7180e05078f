import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, OnChanges } from '@angular/core';
import { BooleanInput, InputBoolean } from '@delon/util';

@Component({
  selector: 'status-label, [status-label]',
  template: `
    <i *ngIf="icon" nz-icon [nzType]="iconType" class="pr-xs"></i>
    {{ text }}
    <ng-content></ng-content>
  `,
  host: {
    '[class.text-success]': `_t=='success'`,
    '[class.text-error]': `_t=='error'`,
    '[class.text-orange]': `_t=='warning'`
  },
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class StatusLabelComponent implements OnChanges {
  static ngAcceptInputType_icon: BooleanInput;

  _t?: string;

  iconType!: string;

  @Input()
  set type(v: 'success' | 'error' | 'warning') {
    let iconType: string;
    switch (v) {
      case 'success':
        iconType = 'check-circle';
        break;
      case 'error':
        iconType = 'close-circle';
        break;
      case 'warning':
      default:
        iconType = 'exclamation-circle';
        break;
    }
    this._t = v;
    this.iconType = iconType;
  }

  @Input() @InputBoolean() icon = true;

  @Input() text?: string;

  constructor(private cdr: ChangeDetectorRef) {
    this.type = 'success';
  }

  ngOnChanges(): void {
    this.cdr.detectChanges();
  }
}
