import { Component, ElementRef, ViewChild } from '@angular/core';
import { ICellRendererAngularComp } from 'ag-grid-angular';
import { IAfterGuiAttachedParams } from 'ag-grid-community';
import { NzUploadFile } from 'ng-zorro-antd/upload';

@Component({
  selector: 'app-btn-cell-render',
  templateUrl: './btn-cell-render.component.html'
})
export class BtnCellRenderComponent implements ICellRendererAngularComp {
  constructor() {}

  params: any;
  visibleProcess: boolean = false;
  refresh(params: any): boolean {
    throw new Error('Method not implemented.');
  }
  @ViewChild('fileInput', { static: false }) fileInput!: ElementRef;
  afterGuiAttached?(params?: IAfterGuiAttachedParams): void {
    throw new Error('Method not implemented.');
  }

  agInit(params: any): void {
    this.params = params;
  }

  onShowPopover(): void {
    this.visibleProcess = true;
  }

  btnInfoClickedHandler($event: any): any {
    this.params.infoClicked(this.params.data);
  }

  btnStartWorkflowClickedHandler($event: any): any {
    this.params.startWorkflowClicked(this.params.data);
  }

  btnProcessWorkflowClickedHandler(command: any): any {
    this.params.processWorkflowClicked(this.params.data, command);
  }

  btnViewHistoryWorkflowClickedHandler($event: any): any {
    this.params.viewHistoryWorkflowClicked(this.params.data);
  }

  btnEditClickedHandler($event: any): any {
    this.params.editClicked(this.params.data);
  }
  btnThamDinhClickedHandler($event: any): any {
    this.params.thamDinhClicked(this.params.data);
  }
  btnNghiemThuClickedHandler($event: any): any {
    this.params.nghiemThuClicked(this.params.data);
  }

  btnDeleteClickedHandler($event: any): any {
    this.params.deleteClicked(this.params.data);
  }

  btnPrintClickedHandler($event: any): any {
    this.params.printClicked(this.params.data);
  }

  triggerFileUpload(): void {
    this.fileInput.nativeElement.click();
  }

  onFileSelected(event: any): void {
    const file: File = event.target.files?.[0];
    if (!file) return;

    const nzFile: NzUploadFile = {
      uid: `${Date.now()}`,
      name: file.name,
      status: 'done',
      originFileObj: file,
      size: file.size,
      type: file.type
    };

    this.params?.uploadClicked?.(nzFile, this.params.data);

    // Reset input để lần sau chọn lại file cũ vẫn gọi được change
    this.fileInput.nativeElement.value = '';
  }
  btnSendMailForgotPasswordClickedHandler($event: any): any {
    this.params.sendMailForgotPasswordClicked(this.params.data);
  }

  /// Old

  btnRecordHistoryClickedHandler($event: any): any {
    this.params.historyRecordsClicked(this.params.data);
  }

  btnUpdateStatusReceiOrRejectRecordsClickedHandler($event: any): any {
    this.params.updateStatusReceiOrRejectRecordsClicked(this.params.data);
  }

  btnLockClickedHandler($event: any): any {
    // console.log(this.params.data);
    this.params.lockClicked(this.params.data);
  }

  btnUnLockClickedHandler($event: any): any {
    // console.log(this.params.data);
    this.params.unlockClicked(this.params.data);
  }

  btnUpdateRoleRecordClickedHandler($event: any): any {
    // console.log(this.params.data);
    this.params.updateRecordAuthorizedClicked(this.params.data);
  }

  btnAddUserRoleClickedHandler($event: any): any {
    // console.log(this.params.data);
    this.params.addUserRoleClicked(this.params.data);
  }

  btnAddRoleClickedHandler($event: any): any {
    // console.log(this.params.data);
    this.params.addRoleClicked(this.params.data);
  }

  btnUpdateUserRole($event: any): any {
    this.params.btnUpdateUserRole(this.params.data);
  }

  btnAddPermissionClickedHandler($event: any): any {
    this.params.addPermissionClickedHandler(this.params.data);
  }

  btnAddRoleApiClickedHandler($event: any): any {
    this.params.addRoleApiClickedHandler(this.params.data);
  }

  btnAddMenuClickedHandler($event: any): any {
    this.params.addMenuClickedHandler(this.params.data);
  }

  btnPrintRecordsClickedHandler($event: any): any {
    this.params.printRecordsClicked(this.params.data);
  }

  btnMoveToEndClickedHandler($event: any): any {
    this.params.moveToEndClicked(this.params.data);
  }

  btnMoveToProcessedClickedHandler($event: any): any {
    this.params.moveToProcessedClicked(this.params.data);
  }

  btnMoveToProcessingClickedHandler($event: any): any {
    this.params.moveToProcessingClicked(this.params.data);
  }

  btnCheckMenuClickedHandler($event: any): any {
    this.params.addMenuClickedHandler(this.params.data);
  }

  btnActiveClickedHandler($event: any): any {
    this.params.activeClicked(this.params.data);
  }
}
